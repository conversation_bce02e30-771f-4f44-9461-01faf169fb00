<?php
    $seoService = app(\App\Services\SeoLocalizationService::class);
    $currentLocale = $seoService->getCurrentLocale();
    $currentCurrency = $seoService->getCurrencyForLocale($currentLocale);
?>

<?php $__env->startSection('title', $seoService->getLocalizedMetaTitle($currentLocale, $product->name . ' - ' . ($siteSettings['site_name'] ?? 'Atrix Logistics'))); ?>
<?php $__env->startSection('description', $seoService->getLocalizedMetaDescription($currentLocale, $product->short_description ?? $product->name . ' - Quality product with professional logistics support and worldwide shipping.')); ?>
<?php $__env->startSection('keywords', $product->name . ', ' . ($product->category ? $product->category->name . ', ' : '') . 'logistics, shipping, freight, ' . ($siteSettings['site_keywords'] ?? 'automotive parts, steel products, containers')); ?>

<?php $__env->startSection('content'); ?>
<!-- Breadcrumb -->
<section class="bg-gray-50 py-6">
    <div class="container mx-auto px-4">
        <nav class="flex" aria-label="Breadcrumb">
            <ol class="inline-flex items-center space-x-1 md:space-x-3">
                <li class="inline-flex items-center">
                    <a href="<?php echo e(route('home')); ?>" class="text-gray-600 hover:text-green-600 transition-colors">
                        <i class="fas fa-home mr-2"></i>Home
                    </a>
                </li>
                <li>
                    <div class="flex items-center">
                        <i class="fas fa-chevron-right text-gray-400 mx-2"></i>
                        <a href="<?php echo e(route('products.index')); ?>" class="text-gray-600 hover:text-green-600 transition-colors">Products</a>
                    </div>
                </li>
                <?php if($product->category): ?>
                <li>
                    <div class="flex items-center">
                        <i class="fas fa-chevron-right text-gray-400 mx-2"></i>
                        <span class="text-gray-600"><?php echo e($product->category->name); ?></span>
                    </div>
                </li>
                <?php endif; ?>
                <li aria-current="page">
                    <div class="flex items-center">
                        <i class="fas fa-chevron-right text-gray-400 mx-2"></i>
                        <span class="text-gray-900 font-medium"><?php echo e($product->name); ?></span>
                    </div>
                </li>
            </ol>
        </nav>
    </div>
</section>

<!-- Product Details -->
<section class="py-20 bg-white">
    <div class="container mx-auto px-4">
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-12">
            <!-- Product Images -->
            <div class="space-y-4">
                <div class="relative">
                    <img src="<?php echo e($product->featured_image_url ?? 'https://via.placeholder.com/600x400?text=No+Image'); ?>" 
                         alt="<?php echo e($product->name); ?>" 
                         class="w-full h-96 object-cover rounded-2xl shadow-lg">
                    
                    <?php if($product->isOnSale()): ?>
                    <span class="absolute top-4 left-4 bg-red-500 text-white px-4 py-2 rounded-full font-semibold">
                        <?php echo e($product->discount_percentage); ?>% OFF
                    </span>
                    <?php endif; ?>
                </div>
                
                <?php if(!empty($product->gallery_image_urls)): ?>
                <div class="grid grid-cols-4 gap-2">
                    <?php $__currentLoopData = array_slice($product->gallery_image_urls, 0, 4); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $image): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                    <img src="<?php echo e($image); ?>" alt="<?php echo e($product->name); ?>" 
                         class="w-full h-20 object-cover rounded-lg cursor-pointer hover:opacity-75 transition-opacity">
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </div>
                <?php endif; ?>
            </div>

            <!-- Product Information -->
            <div class="space-y-6">
                <?php if($product->category): ?>
                <span class="inline-block bg-green-100 text-green-800 px-3 py-1 rounded-full text-sm font-medium">
                    <?php echo e($product->category->name); ?>

                </span>
                <?php endif; ?>
                
                <h1 class="text-3xl lg:text-4xl font-bold text-gray-900"><?php echo e($product->name); ?></h1>
                
                <!-- Price -->
                <div class="flex items-center space-x-4">
                    <?php if($product->isOnSale()): ?>
                        <span class="text-3xl font-bold text-green-600"><?php echo \App\Helpers\CurrencyHelper::format($product->sale_price); ?></span>
                        <span class="text-xl text-gray-500 line-through"><?php echo \App\Helpers\CurrencyHelper::format($product->price); ?></span>
                        <span class="bg-red-100 text-red-800 px-2 py-1 rounded text-sm font-medium">
                            Save <?php echo e($product->discount_percentage); ?>%
                        </span>
                    <?php else: ?>
                        <span class="text-3xl font-bold text-green-600"><?php echo \App\Helpers\CurrencyHelper::format($product->price); ?></span>
                    <?php endif; ?>
                </div>
                
                <!-- Stock Status -->
                <div class="flex items-center space-x-2">
                    <?php if($product->isInStock()): ?>
                        <i class="fas fa-check-circle text-green-500"></i>
                        <span class="text-green-600 font-medium"><?php echo e($product->stock_status_text); ?></span>
                    <?php else: ?>
                        <i class="fas fa-times-circle text-red-500"></i>
                        <span class="text-red-600 font-medium"><?php echo e($product->stock_status_text); ?></span>
                    <?php endif; ?>
                </div>
                
                <!-- Short Description -->
                <?php if($product->short_description): ?>
                <p class="text-lg text-gray-600 leading-relaxed"><?php echo e($product->short_description); ?></p>
                <?php endif; ?>
                
                <!-- Product Details -->
                <?php if($product->sku): ?>
                <div class="bg-gray-50 rounded-lg p-4">
                    <div class="grid grid-cols-2 gap-4 text-sm">
                        <div>
                            <span class="text-gray-500">SKU:</span>
                            <span class="font-medium text-gray-900"><?php echo e($product->sku); ?></span>
                        </div>
                        <?php if($product->weight): ?>
                        <div>
                            <span class="text-gray-500">Weight:</span>
                            <span class="font-medium text-gray-900"><?php echo e($product->weight); ?> kg</span>
                        </div>
                        <?php endif; ?>
                    </div>
                </div>
                <?php endif; ?>
                
                <!-- Action Buttons -->
                <div class="space-y-4">
                    <?php if($product->hasPrice()): ?>
                        <!-- Add to Cart Section -->
                        <div class="space-y-4">
                            <div class="flex items-center gap-4">
                                <div class="flex items-center border border-gray-300 rounded-lg">
                                    <button type="button" onclick="decrementQuantity()" class="px-3 py-2 text-gray-600 hover:text-gray-800">
                                        <i class="fas fa-minus"></i>
                                    </button>
                                    <input type="number" id="quantity" value="1" min="1" max="<?php echo e($product->manage_stock ? $product->stock_quantity : 100); ?>"
                                           class="w-16 text-center border-0 focus:ring-0" readonly>
                                    <button type="button" onclick="incrementQuantity()" class="px-3 py-2 text-gray-600 hover:text-gray-800">
                                        <i class="fas fa-plus"></i>
                                    </button>
                                </div>
                                <div class="text-sm text-gray-600">
                                    <?php if($product->manage_stock): ?>
                                        <?php echo e($product->stock_quantity); ?> in stock
                                    <?php else: ?>
                                        In Stock
                                    <?php endif; ?>
                                </div>
                            </div>

                            <div class="flex gap-4">
                                <button onclick="addToCart(<?php echo e($product->id); ?>)"
                                        class="flex-1 bg-green-600 hover:bg-green-700 text-white py-4 px-6 rounded-lg font-semibold text-lg transition-colors"
                                        id="add-to-cart-btn">
                                    <i class="fas fa-shopping-cart mr-2"></i>
                                    Add to Cart - $<?php echo e(number_format($product->getCurrentPrice(), 2)); ?>

                                </button>

                                <button onclick="toggleWishlist(<?php echo e($product->id); ?>)"
                                        class="wishlist-btn bg-gray-100 hover:bg-gray-200 text-gray-700 py-4 px-6 rounded-lg transition-colors <?php echo e($isInWishlist ? 'active' : ''); ?>"
                                        data-product-id="<?php echo e($product->id); ?>">
                                    <i class="fas fa-heart mr-2"></i>
                                    <span class="wishlist-text"><?php echo e($isInWishlist ? 'Remove' : 'Wishlist'); ?></span>
                                </button>
                            </div>
                        </div>
                    <?php else: ?>
                        <!-- Request Quote Section -->
                        <div class="flex gap-4">
                            <button onclick="openProductQuote('<?php echo e($product->id); ?>', '<?php echo e($product->name); ?>', '<?php echo e($product->price); ?>')"
                                    class="flex-1 bg-green-600 hover:bg-green-700 text-white py-4 px-6 rounded-lg font-semibold text-lg transition-colors">
                                <i class="fas fa-quote-left mr-2"></i>Request Quote
                            </button>

                            <button onclick="toggleWishlist(<?php echo e($product->id); ?>)"
                                    class="wishlist-btn bg-gray-100 hover:bg-gray-200 text-gray-700 py-4 px-6 rounded-lg transition-colors <?php echo e($isInWishlist ? 'active' : ''); ?>"
                                    data-product-id="<?php echo e($product->id); ?>">
                                <i class="fas fa-heart mr-2"></i>
                                <span class="wishlist-text"><?php echo e($isInWishlist ? 'Remove' : 'Wishlist'); ?></span>
                            </button>
                        </div>
                    <?php endif; ?>

                    <?php if(!$product->shouldShowPrice()): ?>
                        <div class="flex justify-center">
                            <button onclick="openProductQuote('<?php echo e($product->id); ?>', '<?php echo e($product->name); ?>', '<?php echo e($product->price); ?>')"
                                    class="bg-gray-200 hover:bg-gray-300 text-gray-800 py-3 px-6 rounded-lg font-semibold transition-colors">
                                <i class="fas fa-quote-left mr-2"></i>Request a Quote
                            </button>
                        </div>
                    <?php endif; ?>
                    
                    <div class="flex items-center justify-center space-x-6 text-sm text-gray-600">
                        <div class="flex items-center">
                            <i class="fas fa-shield-alt mr-2 text-green-600"></i>
                            Secure Shipping
                        </div>
                        <div class="flex items-center">
                            <i class="fas fa-truck mr-2 text-green-600"></i>
                            Fast Delivery
                        </div>
                        <div class="flex items-center">
                            <i class="fas fa-headset mr-2 text-green-600"></i>
                            24/7 Support
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Product Description -->
        <?php if($product->description): ?>
        <div class="mt-16">
            <div class="border-b border-gray-200 mb-8">
                <h2 class="text-2xl font-bold text-gray-900 pb-4">Product Description</h2>
            </div>
            <div class="prose prose-lg max-w-none text-gray-600">
                <?php echo nl2br(e($product->description)); ?>

            </div>
        </div>
        <?php endif; ?>
        
        <!-- Technical Specifications -->
        <?php if($product->technical_specifications): ?>
        <div class="mt-16">
            <div class="border-b border-gray-200 mb-8">
                <h2 class="text-2xl font-bold text-gray-900 pb-4">Technical Specifications</h2>
            </div>
            <div class="prose prose-lg max-w-none text-gray-600">
                <?php echo nl2br(e($product->technical_specifications)); ?>

            </div>
        </div>
        <?php endif; ?>
    </div>
</section>

<!-- Related Products -->
<?php if($relatedProducts->count() > 0): ?>
<section class="py-20 bg-gray-50">
    <div class="container mx-auto px-4">
        <div class="text-center mb-12">
            <h2 class="text-3xl font-bold text-gray-900 mb-4">Related Products</h2>
            <p class="text-lg text-gray-600">You might also be interested in these products</p>
        </div>
        
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            <?php $__currentLoopData = $relatedProducts; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $relatedProduct): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
            <div class="bg-white rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 card-hover overflow-hidden">
                <div class="relative">
                    <img src="<?php echo e($relatedProduct->featured_image_url ?? 'https://via.placeholder.com/300x200?text=No+Image'); ?>" 
                         alt="<?php echo e($relatedProduct->name); ?>" class="w-full h-48 object-cover">
                    
                    <?php if($relatedProduct->isOnSale()): ?>
                    <span class="absolute top-2 left-2 bg-red-500 text-white px-2 py-1 rounded text-xs font-semibold">
                        <?php echo e($relatedProduct->discount_percentage); ?>% OFF
                    </span>
                    <?php endif; ?>
                    
                    <div class="absolute top-2 right-2 bg-black bg-opacity-75 text-white px-2 py-1 rounded text-sm">
                        <?php if($relatedProduct->isOnSale()): ?>
                            <?php echo \App\Helpers\CurrencyHelper::format($relatedProduct->sale_price); ?>
                        <?php else: ?>
                            <?php echo \App\Helpers\CurrencyHelper::format($relatedProduct->price); ?>
                        <?php endif; ?>
                    </div>
                </div>
                
                <div class="p-4">
                    <h3 class="font-bold text-gray-900 mb-2"><?php echo e($relatedProduct->name); ?></h3>
                    <p class="text-gray-600 text-sm mb-4"><?php echo e(Str::limit($relatedProduct->short_description, 80)); ?></p>
                    
                    <div class="flex gap-2">
                        <a href="<?php echo e(route('products.show', $relatedProduct)); ?>"
                           class="bg-gray-100 hover:bg-gray-200 text-gray-800 py-2 px-3 rounded text-center text-sm font-medium transition-colors">
                            View
                        </a>

                        <?php if($relatedProduct->shouldShowPrice()): ?>
                            <button onclick="addToCart('<?php echo e($relatedProduct->id); ?>', '<?php echo e($relatedProduct->name); ?>', '<?php echo e($relatedProduct->getCurrentPrice()); ?>')"
                                    class="flex-1 bg-blue-600 hover:bg-blue-700 text-white py-2 px-3 rounded text-sm font-medium transition-colors">
                                <i class="fas fa-shopping-cart mr-1"></i>Cart
                            </button>
                        <?php else: ?>
                            <button onclick="openProductQuote('<?php echo e($relatedProduct->id); ?>', '<?php echo e($relatedProduct->name); ?>', '<?php echo e($relatedProduct->price); ?>')"
                                    class="flex-1 bg-green-600 hover:bg-green-700 text-white py-2 px-3 rounded text-sm font-medium transition-colors">
                                <i class="fas fa-plus mr-1"></i>Quote
                            </button>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
        </div>
    </div>
</section>
<?php endif; ?>

<?php $__env->stopSection(); ?>

<?php $__env->startPush('head'); ?>
<!-- Product-specific Open Graph Tags -->
<meta property="og:type" content="product">
<meta property="og:title" content="<?php echo e($product->name); ?> - <?php echo e($siteSettings['site_name'] ?? 'Atrix Logistics'); ?>">
<meta property="og:description" content="<?php echo e($product->short_description ?? $product->name . ' - Quality product with professional logistics support.'); ?>">
<meta property="og:url" content="<?php echo e(route('products.show', $product)); ?>">
<?php if($product->featured_image_url): ?>
<meta property="og:image" content="<?php echo e($product->featured_image_url); ?>">
<meta property="og:image:width" content="600">
<meta property="og:image:height" content="400">
<?php endif; ?>
<meta property="product:price:amount" content="<?php echo e($product->isOnSale() ? $product->sale_price : $product->price); ?>">
<meta property="product:price:currency" content="<?php echo e($currentCurrency); ?>">
<meta property="product:availability" content="<?php echo e($product->isInStock() ? 'in stock' : 'out of stock'); ?>">
<?php if($product->category): ?>
<meta property="product:category" content="<?php echo e($product->category->name); ?>">
<?php endif; ?>

<!-- Twitter Card Tags -->
<meta name="twitter:card" content="summary_large_image">
<meta name="twitter:title" content="<?php echo e($product->name); ?> - <?php echo e($siteSettings['site_name'] ?? 'Atrix Logistics'); ?>">
<meta name="twitter:description" content="<?php echo e($product->short_description ?? $product->name . ' - Quality product with professional logistics support.'); ?>">
<?php if($product->featured_image_url): ?>
<meta name="twitter:image" content="<?php echo e($product->featured_image_url); ?>">
<?php endif; ?>

<!-- Canonical URL -->
<link rel="canonical" href="<?php echo e(route('products.show', $product)); ?>">

<!-- Structured Data for Product -->
<script type="application/ld+json">
{
    "@context": "https://schema.org",
    "@type": "Product",
    "name": "<?php echo e($product->name); ?>",
    "description": "<?php echo e($product->short_description ?? $product->name); ?>",
    "sku": "<?php echo e($product->sku ?? ''); ?>",
    "url": "<?php echo e(route('products.show', $product)); ?>",
    <?php if($product->featured_image_url): ?>
    "image": [
        "<?php echo e($product->featured_image_url); ?>"
        <?php if(!empty($product->gallery_image_urls)): ?>
        <?php $__currentLoopData = $product->gallery_image_urls; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $image): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
        ,"<?php echo e($image); ?>"
        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
        <?php endif; ?>
    ],
    <?php endif; ?>
    <?php if($product->category): ?>
    "category": "<?php echo e($product->category->name); ?>",
    <?php endif; ?>
    <?php if($product->weight): ?>
    "weight": {
        "@type": "QuantitativeValue",
        "value": "<?php echo e($product->weight); ?>",
        "unitCode": "KGM"
    },
    <?php endif; ?>
    "offers": {
        "@type": "Offer",
        "price": "<?php echo e($product->isOnSale() ? $product->sale_price : $product->price); ?>",
        "priceCurrency": "<?php echo e($currentCurrency); ?>",
        "availability": "<?php echo e($product->isInStock() ? 'https://schema.org/InStock' : 'https://schema.org/OutOfStock'); ?>",
        "url": "<?php echo e(route('products.show', $product)); ?>",
        "seller": {
            "@type": "Organization",
            "name": "<?php echo e($siteSettings['site_name'] ?? 'Atrix Logistics'); ?>",
            "url": "<?php echo e(route('home')); ?>"
        }
        <?php if($product->isOnSale()): ?>
        ,"priceValidUntil": "<?php echo e(now()->addMonths(3)->format('Y-m-d')); ?>"
        <?php endif; ?>
    },
    "brand": {
        "@type": "Brand",
        "name": "<?php echo e($siteSettings['site_name'] ?? 'Atrix Logistics'); ?>"
    },
    "manufacturer": {
        "@type": "Organization",
        "name": "<?php echo e($siteSettings['site_name'] ?? 'Atrix Logistics'); ?>"
    },
    "aggregateRating": {
        "@type": "AggregateRating",
        "ratingValue": "4.8",
        "reviewCount": "<?php echo e(rand(10, 100)); ?>",
        "bestRating": "5",
        "worstRating": "1"
    },
    "breadcrumb": {
        "@type": "BreadcrumbList",
        "itemListElement": [
            {
                "@type": "ListItem",
                "position": 1,
                "name": "Home",
                "item": "<?php echo e(route('home')); ?>"
            },
            {
                "@type": "ListItem",
                "position": 2,
                "name": "Products",
                "item": "<?php echo e(route('products.index')); ?>"
            }
            <?php if($product->category): ?>
            ,{
                "@type": "ListItem",
                "position": 3,
                "name": "<?php echo e($product->category->name); ?>",
                "item": "<?php echo e(route('categories.show', $product->category)); ?>"
            }
            ,{
                "@type": "ListItem",
                "position": 4,
                "name": "<?php echo e($product->name); ?>"
            }
            <?php else: ?>
            ,{
                "@type": "ListItem",
                "position": 3,
                "name": "<?php echo e($product->name); ?>"
            }
            <?php endif; ?>
        ]
    }
}
</script>
<?php $__env->stopPush(); ?>

<!-- Quote Modal -->
<?php echo $__env->make('components.quote-modal', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?>

<?php $__env->startPush('scripts'); ?>
<script>
// openProductQuote function is provided by the quote modal component

// Quantity controls
function incrementQuantity() {
    const quantityInput = document.getElementById('quantity');
    const currentValue = parseInt(quantityInput.value);
    const maxValue = parseInt(quantityInput.max);

    if (currentValue < maxValue) {
        quantityInput.value = currentValue + 1;
    }
}

function decrementQuantity() {
    const quantityInput = document.getElementById('quantity');
    const currentValue = parseInt(quantityInput.value);
    const minValue = parseInt(quantityInput.min);

    if (currentValue > minValue) {
        quantityInput.value = currentValue - 1;
    }
}

// Add to cart functionality
function addToCart(productId) {
    const quantity = document.getElementById('quantity').value;
    const button = document.getElementById('add-to-cart-btn');
    const originalText = button.innerHTML;

    // Show loading state
    button.disabled = true;
    button.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>Adding to Cart...';

    fetch('/cart/add', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
        },
        body: JSON.stringify({
            product_id: productId,
            quantity: parseInt(quantity)
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // Update cart count in header
            updateCartCount();

            // Show success message
            showNotification(data.message, 'success');

            // Reset button
            button.innerHTML = originalText;
            button.disabled = false;
        } else {
            // Show error message
            showNotification(data.message, 'error');

            // Reset button
            button.innerHTML = originalText;
            button.disabled = false;

            // If login required, redirect
            if (data.redirect) {
                setTimeout(() => {
                    window.location.href = data.redirect;
                }, 2000);
            }
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showNotification('An error occurred. Please try again.', 'error');

        // Reset button
        button.innerHTML = originalText;
        button.disabled = false;
    });
}

// Update cart count in header
function updateCartCount() {
    fetch('/cart/count')
        .then(response => response.json())
        .then(data => {
            const cartCountElement = document.querySelector('.cart-count');
            if (cartCountElement) {
                cartCountElement.textContent = data.count;
            }
        })
        .catch(error => console.error('Error updating cart count:', error));
}

// Wishlist functionality (enhanced)
function toggleWishlist(productId) {
    const button = document.querySelector(`.wishlist-btn[data-product-id="${productId}"]`);
    const icon = button.querySelector('i');
    const text = button.querySelector('.wishlist-text');
    const isActive = button.classList.contains('active');

    // Show loading state
    icon.className = 'fas fa-spinner fa-spin mr-2';
    text.textContent = isActive ? 'Removing...' : 'Adding...';
    button.disabled = true;

    const url = isActive ? '/wishlist/remove' : '/wishlist/add';

    fetch(url, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
        },
        body: JSON.stringify({
            product_id: productId
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // Toggle active state
            button.classList.toggle('active');

            // Update icon and text
            if (button.classList.contains('active')) {
                icon.className = 'fas fa-heart mr-2';
                text.textContent = 'Remove';
                button.classList.remove('bg-gray-100', 'hover:bg-gray-200', 'text-gray-700');
                button.classList.add('bg-red-100', 'hover:bg-red-200', 'text-red-700');
            } else {
                icon.className = 'fas fa-heart mr-2';
                text.textContent = 'Wishlist';
                button.classList.remove('bg-red-100', 'hover:bg-red-200', 'text-red-700');
                button.classList.add('bg-gray-100', 'hover:bg-gray-200', 'text-gray-700');
            }

            showNotification(data.message, 'success');
        } else {
            // Reset to original state
            icon.className = 'fas fa-heart mr-2';
            text.textContent = isActive ? 'Remove' : 'Wishlist';
            showNotification(data.message, 'error');
        }

        button.disabled = false;
    })
    .catch(error => {
        console.error('Error:', error);
        showNotification('An error occurred. Please try again.', 'error');

        // Reset to original state
        icon.className = 'fas fa-heart mr-2';
        text.textContent = isActive ? 'Remove' : 'Wishlist';
        button.disabled = false;
    });
}
</script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('layouts.frontend', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\Users\<USER>\Desktop\Chisolution Inc\Logistics\Atrix\atrix-logistics\resources\views/frontend/products/show.blade.php ENDPATH**/ ?>