<!-- Quote Modal -->
<div id="quoteModal" class="fixed inset-0 bg-black bg-opacity-50 z-50 hidden flex items-center justify-center p-4">
    <div class="bg-white rounded-2xl max-w-4xl w-full max-h-[90vh] overflow-y-auto">
        <!-- Modal Header -->
        <div class="flex items-center justify-between p-6 border-b border-gray-200">
            <h2 class="text-2xl font-bold text-gray-900">
                <i class="fas fa-quote-left mr-2 text-green-600"></i>Request Quote
            </h2>
            <button type="button" onclick="closeQuoteModal()" class="text-gray-400 hover:text-gray-600 transition-colors">
                <i class="fas fa-times text-xl"></i>
            </button>
        </div>

        <!-- Modal Body -->
        <div class="p-6">
            <!-- Error Display -->
            <div id="quoteErrors" class="bg-red-50 border border-red-200 rounded-lg p-4 mb-6 hidden">
                <div class="flex items-start">
                    <i class="fas fa-exclamation-triangle text-red-500 mr-3 mt-1"></i>
                    <div>
                        <h3 class="text-red-800 font-semibold mb-2">Please fix the following errors:</h3>
                        <ul id="quoteErrorsList" class="text-red-700 space-y-1"></ul>
                    </div>
                </div>
            </div>

            <!-- Success Display -->
            <div id="quoteSuccess" class="bg-green-50 border border-green-200 rounded-lg p-4 mb-6 hidden">
                <div class="flex items-center">
                    <i class="fas fa-check-circle text-green-500 mr-3"></i>
                    <span id="quoteSuccessMessage" class="text-green-800 font-medium"></span>
                </div>
            </div>

            <form id="quoteForm" action="{{ route('customer.quotes.store') }}" method="POST">
                @csrf
                <input type="hidden" id="quote_type" name="quote_type" value="shipping">

                <!-- Quote Type Tabs -->
                <div class="flex border-b border-gray-200 mb-6">
                    <button type="button" id="shipping-tab"
                            class="flex-1 py-3 px-4 text-center font-medium border-b-2 border-green-600 text-green-600 bg-green-50 transition-colors"
                            onclick="switchTab('shipping')">
                        <i class="fas fa-shipping-fast mr-2"></i>Shipping Quote
                    </button>
                    <button type="button" id="product-tab"
                            class="flex-1 py-3 px-4 text-center font-medium border-b-2 border-transparent text-gray-500 hover:text-gray-700 transition-colors"
                            onclick="switchTab('product')">
                        <i class="fas fa-box mr-2"></i>Product Quote
                    </button>
                </div>

                <!-- Tab Content -->
                <div id="quoteTypeContent">
                    <!-- Shipping Quote Tab -->
                    <div id="shipping-quote" class="block">
                        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                            <!-- Customer Information -->
                            <div>
                                <h3 class="text-lg font-semibold text-gray-900 mb-4">Customer Information</h3>
                                <div class="space-y-4">
                                    <div>
                                        <label for="customer_name" class="block text-sm font-medium text-gray-700 mb-2">Full Name *</label>
                                        <input type="text" id="customer_name" name="customer_name" required
                                               value="{{ auth()->check() ? auth()->user()->name : '' }}"
                                               class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:border-green-600 focus:ring-2 focus:ring-green-100 transition-colors">
                                    </div>
                                    <div>
                                        <label for="customer_email" class="block text-sm font-medium text-gray-700 mb-2">Email Address *</label>
                                        <input type="email" id="customer_email" name="customer_email" required
                                               value="{{ auth()->check() ? auth()->user()->email : '' }}"
                                               class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:border-green-600 focus:ring-2 focus:ring-green-100 transition-colors">
                                    </div>
                                    <div>
                                        <label for="customer_phone" class="block text-sm font-medium text-gray-700 mb-2">Phone Number</label>
                                        <input type="tel" id="customer_phone" name="customer_phone"
                                               value="{{ auth()->check() ? auth()->user()->phone : '' }}"
                                               class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:border-green-600 focus:ring-2 focus:ring-green-100 transition-colors">
                                    </div>
                                    <div>
                                        <label for="company_name" class="block text-sm font-medium text-gray-700 mb-2">Company Name</label>
                                        <input type="text" id="company_name" name="company_name"
                                               value="{{ auth()->check() ? auth()->user()->company_name : '' }}"
                                               class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:border-green-600 focus:ring-2 focus:ring-green-100 transition-colors">
                                    </div>
                                </div>
                            </div>

                            <!-- Service Details -->
                            <div>
                                <h3 class="text-lg font-semibold text-gray-900 mb-4">Service Details</h3>
                                <div class="space-y-4">
                                    <div>
                                        <label for="service_type" class="block text-sm font-medium text-gray-700 mb-2">Service Type *</label>
                                        <select id="service_type" name="service_type" required
                                                class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:border-green-600 focus:ring-2 focus:ring-green-100 transition-colors">
                                            <option value="">Select Service Type</option>
                                            <option value="domestic_shipping">Domestic Shipping</option>
                                            <option value="international_shipping">International Shipping</option>
                                            <option value="express_delivery">Express Delivery</option>
                                            <option value="freight_shipping">Freight Shipping</option>
                                            <option value="warehousing">Warehousing</option>
                                            <option value="custom_logistics">Custom Logistics</option>
                                        </select>
                                    </div>
                                    <div>
                                        <label for="priority" class="block text-sm font-medium text-gray-700 mb-2">Priority *</label>
                                        <select id="priority" name="priority" required
                                                class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:border-green-600 focus:ring-2 focus:ring-green-100 transition-colors">
                                            <option value="standard">Standard</option>
                                            <option value="urgent">Urgent</option>
                                            <option value="express">Express</option>
                                        </select>
                                    </div>
                                    <div>
                                        <label for="description" class="block text-sm font-medium text-gray-700 mb-2">Description *</label>
                                        <textarea id="description" name="description" rows="4" required
                                                  placeholder="Describe your shipping requirements..."
                                                  class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:border-green-600 focus:ring-2 focus:ring-green-100 transition-colors resize-vertical"></textarea>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Shipping Addresses -->
                        <div class="mt-8">
                            <h3 class="text-lg font-semibold text-gray-900 mb-4">Shipping Details</h3>
                            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                                <div>
                                    <label for="origin_address" class="block text-sm font-medium text-gray-700 mb-2">Origin Address *</label>
                                    <textarea id="origin_address" name="origin_address" rows="3" required
                                              placeholder="Enter pickup address..."
                                              class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:border-green-600 focus:ring-2 focus:ring-green-100 transition-colors resize-vertical"></textarea>
                                </div>
                                <div>
                                    <label for="destination_address" class="block text-sm font-medium text-gray-700 mb-2">Destination Address *</label>
                                    <textarea id="destination_address" name="destination_address" rows="3" required
                                              placeholder="Enter delivery address..."
                                              class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:border-green-600 focus:ring-2 focus:ring-green-100 transition-colors resize-vertical"></textarea>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Product Quote Tab -->
                    <div id="product-quote" class="hidden">
                        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                            <!-- Customer Information -->
                            <div>
                                <h3 class="text-lg font-semibold text-gray-900 mb-4">Customer Information</h3>
                                <div class="space-y-4">
                                    <div>
                                        <label for="product_customer_name" class="block text-sm font-medium text-gray-700 mb-2">Full Name *</label>
                                        <input type="text" id="product_customer_name" name="customer_name"
                                               value="{{ auth()->check() ? auth()->user()->name : '' }}"
                                               class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:border-green-600 focus:ring-2 focus:ring-green-100 transition-colors">
                                    </div>
                                    <div>
                                        <label for="product_customer_email" class="block text-sm font-medium text-gray-700 mb-2">Email Address *</label>
                                        <input type="email" id="product_customer_email" name="customer_email"
                                               value="{{ auth()->check() ? auth()->user()->email : '' }}"
                                               class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:border-green-600 focus:ring-2 focus:ring-green-100 transition-colors">
                                    </div>
                                    <div>
                                        <label for="product_customer_phone" class="block text-sm font-medium text-gray-700 mb-2">Phone Number</label>
                                        <input type="tel" id="product_customer_phone" name="customer_phone"
                                               value="{{ auth()->check() ? auth()->user()->phone : '' }}"
                                               class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:border-green-600 focus:ring-2 focus:ring-green-100 transition-colors">
                                    </div>
                                    <div>
                                        <label for="product_company_name" class="block text-sm font-medium text-gray-700 mb-2">Company Name</label>
                                        <input type="text" id="product_company_name" name="company_name"
                                               value="{{ auth()->check() ? auth()->user()->company_name : '' }}"
                                               class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:border-green-600 focus:ring-2 focus:ring-green-100 transition-colors">
                                    </div>
                                </div>
                            </div>

                            <!-- Quote Details -->
                            <div>
                                <h3 class="text-lg font-semibold text-gray-900 mb-4">Quote Details</h3>
                                <div class="space-y-4">
                                    <div>
                                        <label for="product_priority" class="block text-sm font-medium text-gray-700 mb-2">Priority *</label>
                                        <select id="product_priority" name="priority"
                                                class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:border-green-600 focus:ring-2 focus:ring-green-100 transition-colors">
                                            <option value="standard">Standard</option>
                                            <option value="urgent">Urgent</option>
                                            <option value="express">Express</option>
                                        </select>
                                    </div>
                                    <div>
                                        <label for="product_description" class="block text-sm font-medium text-gray-700 mb-2">Additional Requirements</label>
                                        <textarea id="product_description" name="description" rows="4"
                                                  placeholder="Any special requirements or questions about the products..."
                                                  class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:border-green-600 focus:ring-2 focus:ring-green-100 transition-colors resize-vertical"></textarea>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Selected Products -->
                        <div class="mt-8">
                            <h3 class="text-lg font-semibold text-gray-900 mb-4">Selected Products</h3>
                            <div id="selectedProducts">
                                <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
                                    <div class="flex items-center">
                                        <i class="fas fa-info-circle text-blue-500 mr-3"></i>
                                        <span class="text-blue-800">No products selected. Use the "Add Product" button to add products to your quote.</span>
                                    </div>
                                </div>
                            </div>
                            <button type="button" onclick="showProductSelector()" class="mt-4 bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg font-semibold transition-colors">
                                <i class="fas fa-plus mr-2"></i>Add Product
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Submit Button -->
                <div class="flex justify-end gap-4 mt-8 pt-6 border-t border-gray-200">
                    <button type="button" onclick="closeQuoteModal()" class="px-6 py-3 border border-gray-300 text-gray-700 rounded-lg font-semibold hover:bg-gray-50 transition-colors">
                        Cancel
                    </button>
                    <button type="submit" class="bg-green-600 hover:bg-green-700 text-white px-8 py-3 rounded-lg font-semibold transition-colors">
                        <i class="fas fa-paper-plane mr-2"></i>Submit Quote Request
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Product Selector Modal -->
<div id="productSelectorModal" class="hidden fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center p-4">
    <div class="bg-white rounded-2xl shadow-2xl max-w-6xl w-full max-h-[90vh] overflow-hidden">
        <div class="flex items-center justify-between p-6 border-b border-gray-200">
            <h2 class="text-2xl font-bold text-gray-900">Select Products</h2>
            <button type="button" onclick="closeProductSelector()" class="text-gray-400 hover:text-gray-600 transition-colors">
                <i class="fas fa-times text-xl"></i>
            </button>
        </div>

        <!-- Search and Filter -->
        <div class="p-6 border-b border-gray-200 bg-gray-50">
            <div class="flex flex-col md:flex-row gap-4">
                <div class="flex-1">
                    <input type="text" id="productSearch" placeholder="Search products..."
                           onkeyup="searchProducts()"
                           class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:border-blue-600 focus:ring-2 focus:ring-blue-100">
                </div>
                <div class="md:w-48">
                    <select id="categoryFilter" onchange="searchProducts()"
                            class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:border-blue-600 focus:ring-2 focus:ring-blue-100">
                        <option value="">All Categories</option>
                        @foreach(\App\Models\Category::active()->orderBy('name')->get() as $category)
                        <option value="{{ $category->slug }}">{{ $category->name }}</option>
                        @endforeach
                    </select>
                </div>
            </div>
        </div>

        <!-- Products List -->
        <div class="p-6 overflow-y-auto max-h-96">
            <div id="productsList">
                <!-- Products will be loaded here -->
            </div>
        </div>

        <!-- Footer -->
        <div class="p-6 border-t border-gray-200 bg-gray-50">
            <div class="flex justify-between items-center">
                <div class="text-sm text-gray-600">
                    <span id="selectedCount">0</span> products selected
                </div>
                <button type="button" onclick="closeProductSelector()"
                        class="bg-green-600 hover:bg-green-700 text-white px-6 py-2 rounded-lg font-semibold transition-colors">
                    Done
                </button>
            </div>
        </div>
    </div>
</div>

<script>
// Quote modal functionality
window.selectedProducts = window.selectedProducts || [];

function openQuoteModal() {
    document.getElementById('quoteModal').classList.remove('hidden');
    document.body.classList.add('overflow-hidden');
}

function closeQuoteModal() {
    document.getElementById('quoteModal').classList.add('hidden');
    document.body.classList.remove('overflow-hidden');
}

function switchTab(tabType) {
    // Update hidden input
    document.getElementById('quote_type').value = tabType;

    // Update tab buttons
    const shippingTab = document.getElementById('shipping-tab');
    const productTab = document.getElementById('product-tab');
    const shippingContent = document.getElementById('shipping-quote');
    const productContent = document.getElementById('product-quote');

    if (tabType === 'shipping') {
        shippingTab.className = 'flex-1 py-3 px-4 text-center font-medium border-b-2 border-green-600 text-green-600 bg-green-50 transition-colors';
        productTab.className = 'flex-1 py-3 px-4 text-center font-medium border-b-2 border-transparent text-gray-500 hover:text-gray-700 transition-colors';
        shippingContent.classList.remove('hidden');
        shippingContent.classList.add('block');
        productContent.classList.remove('block');
        productContent.classList.add('hidden');
    } else {
        productTab.className = 'flex-1 py-3 px-4 text-center font-medium border-b-2 border-green-600 text-green-600 bg-green-50 transition-colors';
        shippingTab.className = 'flex-1 py-3 px-4 text-center font-medium border-b-2 border-transparent text-gray-500 hover:text-gray-700 transition-colors';
        productContent.classList.remove('hidden');
        productContent.classList.add('block');
        shippingContent.classList.remove('block');
        shippingContent.classList.add('hidden');
    }
}

function openProductQuote(productId, productName, productPrice) {
    // Switch to product quote tab
    switchTab('product');

    // Add product to selection
    addProductToQuote(productId, productName, productPrice, 1);

    // Show modal
    openQuoteModal();
}

function addProductToQuote(productId, productName, productPrice, quantity = 1) {
    // Ensure selectedProducts is initialized as an array
    if (!Array.isArray(window.selectedProducts)) {
        window.selectedProducts = [];
    }

    // Check if product already exists
    const existingIndex = window.selectedProducts.findIndex(p => p.id == productId);

    if (existingIndex >= 0) {
        // Update quantity
        window.selectedProducts[existingIndex].quantity += quantity;
    } else {
        // Add new product
        window.selectedProducts.push({
            id: productId,
            name: productName,
            price: productPrice,
            quantity: quantity
        });
    }

    updateSelectedProductsDisplay();
    updateSelectedCount();
}

function removeProductFromQuote(productId) {
    window.selectedProducts = window.selectedProducts.filter(p => p.id != productId);
    updateSelectedProductsDisplay();
    updateSelectedCount();
}

function updateProductQuantity(productId, quantity) {
    const product = window.selectedProducts.find(p => p.id == productId);
    if (product) {
        product.quantity = Math.max(1, parseInt(quantity));
        updateSelectedProductsDisplay();
    }
}

function updateSelectedProductsDisplay() {
    const container = document.getElementById('selectedProducts');

    if (window.selectedProducts.length === 0) {
        container.innerHTML = `
            <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
                <div class="flex items-center">
                    <i class="fas fa-info-circle text-blue-500 mr-3"></i>
                    <span class="text-blue-800">No products selected. Use the "Add Product" button to add products to your quote.</span>
                </div>
            </div>
        `;
        return;
    }

    let html = '';
    let total = 0;

    window.selectedProducts.forEach(product => {
        const subtotal = product.price * product.quantity;
        total += subtotal;

        html += `
            <div class="bg-white border border-gray-200 rounded-lg p-4 mb-3">
                <div class="flex items-center justify-between">
                    <div class="flex-1">
                        <h4 class="font-semibold text-gray-900">${product.name}</h4>
                        <p class="text-sm text-gray-600">${formatCurrency(product.price)} each</p>
                    </div>
                    <div class="flex items-center gap-4">
                        <div class="flex items-center gap-2">
                            <label class="text-sm text-gray-600">Qty:</label>
                            <input type="number" value="${product.quantity}" min="1"
                                   onchange="updateProductQuantity(${product.id}, this.value)"
                                   class="w-16 px-2 py-1 border border-gray-300 rounded text-center">
                        </div>
                        <div class="text-right">
                            <p class="font-semibold text-gray-900">${formatCurrency(subtotal)}</p>
                        </div>
                        <button type="button" onclick="removeProductFromQuote(${product.id})"
                                class="text-red-500 hover:text-red-700 transition-colors">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                </div>
            </div>
        `;
    });

    html += `
        <div class="bg-green-50 border border-green-200 rounded-lg p-4">
            <div class="flex justify-between items-center">
                <span class="font-semibold text-gray-900">Total Value:</span>
                <span class="text-xl font-bold text-green-600">${formatCurrency(total)}</span>
            </div>
        </div>
    `;

    container.innerHTML = html;
}

function showProductSelector() {
    // Show the product selector modal
    document.getElementById('productSelectorModal').classList.remove('hidden');
    document.body.classList.add('overflow-hidden');

    // Load products if not already loaded
    if (!window.productsLoaded) {
        loadProducts();
    }
}

function closeProductSelector() {
    document.getElementById('productSelectorModal').classList.add('hidden');
    document.body.classList.remove('overflow-hidden');
}

function loadProducts() {
    const container = document.getElementById('productsList');
    container.innerHTML = '<div class="text-center py-8"><i class="fas fa-spinner fa-spin text-2xl text-gray-400"></i><p class="mt-2 text-gray-600">Loading products...</p></div>';

    fetch('/api/products')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                displayProducts(data.products);
                window.productsLoaded = true;
            } else {
                container.innerHTML = '<div class="text-center py-8 text-red-600">Error loading products. Please try again.</div>';
            }
        })
        .catch(error => {
            console.error('Error loading products:', error);
            container.innerHTML = '<div class="text-center py-8 text-red-600">Error loading products. Please try again.</div>';
        });
}

function displayProducts(products) {
    const container = document.getElementById('productsList');

    // Store products data for later use
    container.dataset.products = JSON.stringify(products);

    if (products.length === 0) {
        container.innerHTML = '<div class="text-center py-8 text-gray-600">No products available.</div>';
        return;
    }

    let html = '<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">';

    products.forEach(product => {
        const isSelected = window.selectedProducts.some(p => p.id == product.id);
        const price = product.price || 0;
        const priceDisplay = price > 0 ? formatCurrency(price) : 'Quote Only';

        // Escape product name for JavaScript
        const escapedName = product.name.replace(/'/g, "\\'").replace(/"/g, '\\"');

        html += `
            <div class="bg-white border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow">
                <div class="aspect-w-16 aspect-h-12 mb-3">
                    <img src="${product.featured_image_url || 'https://via.placeholder.com/300x200?text=No+Image'}"
                         alt="${escapedName}"
                         class="w-full h-32 object-cover rounded">
                </div>
                <h4 class="font-semibold text-gray-900 mb-2 line-clamp-2">${product.name}</h4>
                <p class="text-sm text-gray-600 mb-2 line-clamp-2">${product.short_description || ''}</p>
                <div class="flex items-center justify-between">
                    <span class="font-bold text-green-600">${priceDisplay}</span>
                    <button onclick="selectProductForQuote(${product.id}, '${escapedName}', ${price})"
                            class="px-3 py-1 text-sm ${isSelected ? 'bg-green-600 text-white' : 'bg-blue-600 hover:bg-blue-700 text-white'} rounded transition-colors">
                        ${isSelected ? 'Selected' : 'Select'}
                    </button>
                </div>
            </div>
        `;
    });

    html += '</div>';
    container.innerHTML = html;
}

function selectProductForQuote(productId, productName, productPrice) {
    addProductToQuote(productId, productName, productPrice, 1);

    // Update the selected count
    updateSelectedCount();

    // Update the display in the product selector
    if (window.productsLoaded) {
        // Refresh the products display to show updated selection state
        const container = document.getElementById('productsList');
        const products = JSON.parse(container.dataset.products || '[]');
        if (products.length > 0) {
            displayProducts(products);
        }
    }

    // Show success message
    if (typeof showNotification === 'function') {
        showNotification(`${productName} added to quote`, 'success');
    }
}

function updateSelectedCount() {
    const countElement = document.getElementById('selectedCount');
    if (countElement) {
        countElement.textContent = window.selectedProducts.length;
    }
}

// Add utility functions
function formatCurrency(amount) {
    return new Intl.NumberFormat('en-US', {
        style: 'currency',
        currency: 'USD'
    }).format(amount);
}

// Fallback notification function if not available globally
function showNotification(message, type = 'info') {
    if (typeof window.showNotification === 'function') {
        window.showNotification(message, type);
    } else {
        // Simple fallback
        alert(message);
    }
}

function searchProducts() {
    const searchTerm = document.getElementById('productSearch').value.toLowerCase();
    const categoryFilter = document.getElementById('categoryFilter').value;

    fetch(`/api/products?search=${encodeURIComponent(searchTerm)}&category=${encodeURIComponent(categoryFilter)}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                displayProducts(data.products);
            }
        })
        .catch(error => {
            console.error('Error searching products:', error);
        });
}

// Form submission handler
document.addEventListener('DOMContentLoaded', function() {
    const form = document.getElementById('quoteForm');
    if (form) {
        form.addEventListener('submit', function(e) {
            e.preventDefault();

            // Hide previous messages
            document.getElementById('quoteErrors').classList.add('hidden');
            document.getElementById('quoteSuccess').classList.add('hidden');

            // Validate form fields
            const validationErrors = validateQuoteForm();
            if (validationErrors.length > 0) {
                showQuoteError(validationErrors);
                return;
            }

            const quoteType = document.getElementById('quote_type').value;
            const formData = new FormData(this);

            if (quoteType === 'product') {
                // Validate products are selected
                if (window.selectedProducts.length === 0) {
                    showQuoteError(['Please select at least one product for your quote.']);
                    return;
                }

                // Add products data
                formData.append('products', JSON.stringify(window.selectedProducts.map(p => ({
                    product_id: p.id,
                    quantity: p.quantity,
                    price_at_time: p.price
                }))));

                // Calculate total
                const total = window.selectedProducts.reduce((sum, p) => sum + (p.price * p.quantity), 0);
                formData.append('products_total', total.toFixed(2));
            }

            // Submit form via AJAX
            fetch(this.action, {
                method: 'POST',
                body: formData,
                headers: {
                    'X-Requested-With': 'XMLHttpRequest',
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showQuoteSuccess(data.message);
                    // Optionally close modal after success
                    setTimeout(() => {
                        closeQuoteModal();
                        // Redirect if needed
                        if (data.redirect) {
                            window.location.href = data.redirect;
                        }
                    }, 2000);
                } else {
                    showQuoteError(data.errors || ['An error occurred while submitting your quote.']);
                }
            })
            .catch(error => {
                console.error('Quote submission error:', error);
                showQuoteError(['An error occurred while submitting your quote. Please try again.']);
            });
        });
    }
});

function validateQuoteForm() {
    const errors = [];
    const quoteType = document.getElementById('quote_type').value;

    // Get fields based on active tab
    let customerName, customerEmail, customerPhone, companyName;

    if (quoteType === 'product') {
        customerName = document.getElementById('product_customer_name')?.value?.trim() || '';
        customerEmail = document.getElementById('product_customer_email')?.value?.trim() || '';
        customerPhone = document.getElementById('product_customer_phone')?.value?.trim() || '';
        companyName = document.getElementById('product_company_name')?.value?.trim() || '';
    } else {
        customerName = document.getElementById('customer_name')?.value?.trim() || '';
        customerEmail = document.getElementById('customer_email')?.value?.trim() || '';
        customerPhone = document.getElementById('customer_phone')?.value?.trim() || '';
        companyName = document.getElementById('company_name')?.value?.trim() || '';
    }

    // Basic validation for all quote types
    if (!customerName) {
        errors.push('Customer name is required.');
    }

    if (!customerEmail) {
        errors.push('Email address is required.');
    } else if (!isValidEmail(customerEmail)) {
        errors.push('Please enter a valid email address.');
    }

    // Check quote type specific validation
    if (quoteType === 'product') {
        if (!Array.isArray(window.selectedProducts) || window.selectedProducts.length === 0) {
            errors.push('Please select at least one product for your quote.');
        }

        // For product quotes, description is optional
        // Priority is handled by the form default value
    } else if (quoteType === 'shipping') {
        // Shipping-specific validation
        const serviceTypeElement = document.getElementById('service_type');
        const descriptionElement = document.getElementById('description');
        const originElement = document.getElementById('origin_address');
        const destinationElement = document.getElementById('destination_address');

        if (serviceTypeElement && !serviceTypeElement.value) {
            errors.push('Please select a service type.');
        }

        if (descriptionElement && !descriptionElement.value.trim()) {
            errors.push('Please provide a description of your requirements.');
        }

        if (originElement && !originElement.value.trim()) {
            errors.push('Origin address is required for shipping quotes.');
        }

        if (destinationElement && !destinationElement.value.trim()) {
            errors.push('Destination address is required for shipping quotes.');
        }
    }

    return errors;
}

function isValidEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
}

function showQuoteError(errors) {
    const errorsList = document.getElementById('quoteErrorsList');
    errorsList.innerHTML = '';

    errors.forEach(error => {
        const li = document.createElement('li');
        li.textContent = error;
        errorsList.appendChild(li);
    });

    document.getElementById('quoteErrors').classList.remove('hidden');
    document.getElementById('quoteErrors').scrollIntoView({ behavior: 'smooth' });
}

function showQuoteSuccess(message) {
    document.getElementById('quoteSuccessMessage').textContent = message;
    document.getElementById('quoteSuccess').classList.remove('hidden');
    document.getElementById('quoteSuccess').scrollIntoView({ behavior: 'smooth' });
}

// Close modal when clicking outside
document.getElementById('quoteModal').addEventListener('click', function(e) {
    if (e.target === this) {
        closeQuoteModal();
    }
});

// Make functions globally available
window.openQuoteModal = openQuoteModal;
window.closeQuoteModal = closeQuoteModal;
window.openProductQuote = openProductQuote;
window.addProductToQuote = addProductToQuote;
</script>
