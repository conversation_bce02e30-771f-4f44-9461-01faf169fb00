
[2025-06-09 17:37:58] local.INFO: Quote store method called {"request_data":{"_token":"KVUw0P7wJ122GcsdgKISEcAMxcqACGWaXtHo3P0C","quote_type":"shipping","service_type":"international_shipping","quote_source":"shipping_tab","customer_name":"namemn","customer_email":"<EMAIL>","customer_phone":null,"company_name":null,"priority":"standard","description":null,"origin_address":"jksdsj","origin_city":"mskdjsdk","destination_address":"kjsdkjs","destination_city":"kjsdks"}} 
[2025-06-09 17:37:58] local.ERROR: SQLSTATE[23000]: Integrity constraint violation: 1048 Column 'description' cannot be null (Connection: mysql, SQL: insert into `quotes` (`quote_type`, `customer_name`, `customer_email`, `customer_phone`, `company_name`, `priority`, `description`, `service_type`, `origin_address`, `origin_city`, `destination_address`, `destination_city`, `quote_source`, `fragile`, `hazardous`, `insurance_required`, `signature_required`, `status`, `quote_number`, `updated_at`, `created_at`) values (shipping, namemn, <EMAIL>, ?, ?, standard, ?, international_shipping, jksdsj, mskdjsdk, kjsdkjs, kjsdks, shipping_tab, 0, 0, 0, 0, pending, QTE202506090855, 2025-06-09 17:37:58, 2025-06-09 17:37:58)) {"exception":"[object] (Illuminate\\Database\\QueryException(code: 23000): SQLSTATE[23000]: Integrity constraint violation: 1048 Column 'description' cannot be null (Connection: mysql, SQL: insert into `quotes` (`quote_type`, `customer_name`, `customer_email`, `customer_phone`, `company_name`, `priority`, `description`, `service_type`, `origin_address`, `origin_city`, `destination_address`, `destination_city`, `quote_source`, `fragile`, `hazardous`, `insurance_required`, `signature_required`, `status`, `quote_number`, `updated_at`, `created_at`) values (shipping, namemn, <EMAIL>, ?, ?, standard, ?, international_shipping, jksdsj, mskdjsdk, kjsdkjs, kjsdks, shipping_tab, 0, 0, 0, 0, pending, QTE202506090855, 2025-06-09 17:37:58, 2025-06-09 17:37:58)) at C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:822)
[stacktrace]
#0 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback('insert into `qu...', Array, Object(Closure))
#1 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\MySqlConnection.php(42): Illuminate\\Database\\Connection->run('insert into `qu...', Array, Object(Closure))
#2 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Processors\\MySqlProcessor.php(35): Illuminate\\Database\\MySqlConnection->insert('insert into `qu...', Array, 'id')
#3 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3796): Illuminate\\Database\\Query\\Processors\\MySqlProcessor->processInsertGetId(Object(Illuminate\\Database\\Query\\Builder), 'insert into `qu...', Array, 'id')
#4 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(2205): Illuminate\\Database\\Query\\Builder->insertGetId(Array, 'id')
#5 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1408): Illuminate\\Database\\Eloquent\\Builder->__call('insertGetId', Array)
#6 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1373): Illuminate\\Database\\Eloquent\\Model->insertAndSetId(Object(Illuminate\\Database\\Eloquent\\Builder), Array)
#7 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1212): Illuminate\\Database\\Eloquent\\Model->performInsert(Object(Illuminate\\Database\\Eloquent\\Builder))
#8 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(1189): Illuminate\\Database\\Eloquent\\Model->save()
#9 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php(399): Illuminate\\Database\\Eloquent\\Builder->Illuminate\\Database\\Eloquent\\{closure}(Object(App\\Models\\Quote))
#10 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(1188): tap(Object(App\\Models\\Quote), Object(Closure))
#11 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Traits\\ForwardsCalls.php(23): Illuminate\\Database\\Eloquent\\Builder->create(Array)
#12 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(2449): Illuminate\\Database\\Eloquent\\Model->forwardCallTo(Object(Illuminate\\Database\\Eloquent\\Builder), 'create', Array)
#13 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(2465): Illuminate\\Database\\Eloquent\\Model->__call('create', Array)
#14 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\app\\Http\\Controllers\\QuoteController.php(128): Illuminate\\Database\\Eloquent\\Model::__callStatic('create', Array)
#15 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(46): App\\Http\\Controllers\\QuoteController->store(Object(Illuminate\\Http\\Request))
#16 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(265): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\QuoteController), 'store')
#17 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(211): Illuminate\\Routing\\Route->runController()
#18 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(808): Illuminate\\Routing\\Route->run()
#19 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#20 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\app\\Http\\Middleware\\LocaleMiddleware.php(44): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#21 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): App\\Http\\Middleware\\LocaleMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#22 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#23 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#24 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(87): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#25 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#26 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#27 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#28 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(120): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#29 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(63): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#30 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(36): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(74): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#34 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#35 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#36 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#37 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#38 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#39 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#40 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#41 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#42 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#43 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#44 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#45 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#46 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#47 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#48 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#49 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#50 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#51 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#52 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#53 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#54 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#55 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#56 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#57 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#58 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#59 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#60 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#61 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#62 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#63 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1219): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#64 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#65 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('C:\\\\Users\\\\<USER>\\\\...')
#66 {main}

[previous exception] [object] (PDOException(code: 23000): SQLSTATE[23000]: Integrity constraint violation: 1048 Column 'description' cannot be null at C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\MySqlConnection.php:53)
[stacktrace]
#0 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\MySqlConnection.php(53): PDOStatement->execute()
#1 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(809): Illuminate\\Database\\MySqlConnection->Illuminate\\Database\\{closure}('insert into `qu...', Array)
#2 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback('insert into `qu...', Array, Object(Closure))
#3 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\MySqlConnection.php(42): Illuminate\\Database\\Connection->run('insert into `qu...', Array, Object(Closure))
#4 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Processors\\MySqlProcessor.php(35): Illuminate\\Database\\MySqlConnection->insert('insert into `qu...', Array, 'id')
#5 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3796): Illuminate\\Database\\Query\\Processors\\MySqlProcessor->processInsertGetId(Object(Illuminate\\Database\\Query\\Builder), 'insert into `qu...', Array, 'id')
#6 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(2205): Illuminate\\Database\\Query\\Builder->insertGetId(Array, 'id')
#7 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1408): Illuminate\\Database\\Eloquent\\Builder->__call('insertGetId', Array)
#8 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1373): Illuminate\\Database\\Eloquent\\Model->insertAndSetId(Object(Illuminate\\Database\\Eloquent\\Builder), Array)
#9 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1212): Illuminate\\Database\\Eloquent\\Model->performInsert(Object(Illuminate\\Database\\Eloquent\\Builder))
#10 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(1189): Illuminate\\Database\\Eloquent\\Model->save()
#11 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php(399): Illuminate\\Database\\Eloquent\\Builder->Illuminate\\Database\\Eloquent\\{closure}(Object(App\\Models\\Quote))
#12 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(1188): tap(Object(App\\Models\\Quote), Object(Closure))
#13 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Traits\\ForwardsCalls.php(23): Illuminate\\Database\\Eloquent\\Builder->create(Array)
#14 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(2449): Illuminate\\Database\\Eloquent\\Model->forwardCallTo(Object(Illuminate\\Database\\Eloquent\\Builder), 'create', Array)
#15 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(2465): Illuminate\\Database\\Eloquent\\Model->__call('create', Array)
#16 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\app\\Http\\Controllers\\QuoteController.php(128): Illuminate\\Database\\Eloquent\\Model::__callStatic('create', Array)
#17 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(46): App\\Http\\Controllers\\QuoteController->store(Object(Illuminate\\Http\\Request))
#18 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(265): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\QuoteController), 'store')
#19 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(211): Illuminate\\Routing\\Route->runController()
#20 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(808): Illuminate\\Routing\\Route->run()
#21 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#22 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\app\\Http\\Middleware\\LocaleMiddleware.php(44): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#23 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): App\\Http\\Middleware\\LocaleMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#24 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#25 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#26 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(87): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#27 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#28 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#29 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(120): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#31 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(63): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#32 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(36): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#34 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#35 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(74): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#36 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#37 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#38 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#39 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#40 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#41 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#42 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#43 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#44 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#45 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#46 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#47 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#48 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#49 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#50 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#51 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#52 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#53 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#54 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#55 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#56 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#57 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#58 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#59 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#60 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#61 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#62 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#63 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#64 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#65 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1219): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#66 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#67 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('C:\\\\Users\\\\<USER>\\\\...')
#68 {main}
"} 
[2025-06-09 17:38:23] local.INFO: Quote store method called {"request_data":{"_token":"KVUw0P7wJ122GcsdgKISEcAMxcqACGWaXtHo3P0C","quote_type":"product","service_type":"product_inquiry","quote_source":"product_tab","customer_name":"namemn","customer_email":"<EMAIL>","customer_phone":null,"company_name":null,"priority":"standard","description":null,"origin_address":"jksdsj","origin_city":"mskdjsdk","destination_address":"kjsdkjs","destination_city":"kjsdks","products":"[{\"product_id\":2,\"quantity\":1,\"price_at_time\":125000},{\"product_id\":3,\"quantity\":1,\"price_at_time\":135000}]","products_total":"260000.00"}} 
[2025-06-09 17:38:23] local.ERROR: SQLSTATE[23000]: Integrity constraint violation: 1048 Column 'description' cannot be null (Connection: mysql, SQL: insert into `quotes` (`quote_type`, `customer_name`, `customer_email`, `customer_phone`, `company_name`, `priority`, `description`, `service_type`, `origin_address`, `origin_city`, `destination_address`, `destination_city`, `products`, `products_total`, `quote_source`, `fragile`, `hazardous`, `insurance_required`, `signature_required`, `status`, `quote_number`, `updated_at`, `created_at`) values (product, namemn, <EMAIL>, ?, ?, standard, ?, product_inquiry, jksdsj, mskdjsdk, kjsdkjs, kjsdks, [{"product_id":2,"product_name":"40ft Standard Dry Container - New (One Trip)","product_sku":"CONT-40-STD-NEW-ZA","quantity":1,"price_at_time":"118000.00","total":118000,"notes":null},{"product_id":3,"product_name":"40ft High Cube Container - New","product_sku":"CONT-40-HC-NEW-ZA","quantity":1,"price_at_time":"135000.00","total":135000,"notes":null}], 253000, product_tab, 0, 0, 0, 0, pending, QTE202506091905, 2025-06-09 17:38:23, 2025-06-09 17:38:23)) {"exception":"[object] (Illuminate\\Database\\QueryException(code: 23000): SQLSTATE[23000]: Integrity constraint violation: 1048 Column 'description' cannot be null (Connection: mysql, SQL: insert into `quotes` (`quote_type`, `customer_name`, `customer_email`, `customer_phone`, `company_name`, `priority`, `description`, `service_type`, `origin_address`, `origin_city`, `destination_address`, `destination_city`, `products`, `products_total`, `quote_source`, `fragile`, `hazardous`, `insurance_required`, `signature_required`, `status`, `quote_number`, `updated_at`, `created_at`) values (product, namemn, <EMAIL>, ?, ?, standard, ?, product_inquiry, jksdsj, mskdjsdk, kjsdkjs, kjsdks, [{\"product_id\":2,\"product_name\":\"40ft Standard Dry Container - New (One Trip)\",\"product_sku\":\"CONT-40-STD-NEW-ZA\",\"quantity\":1,\"price_at_time\":\"118000.00\",\"total\":118000,\"notes\":null},{\"product_id\":3,\"product_name\":\"40ft High Cube Container - New\",\"product_sku\":\"CONT-40-HC-NEW-ZA\",\"quantity\":1,\"price_at_time\":\"135000.00\",\"total\":135000,\"notes\":null}], 253000, product_tab, 0, 0, 0, 0, pending, QTE202506091905, 2025-06-09 17:38:23, 2025-06-09 17:38:23)) at C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:822)
[stacktrace]
#0 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback('insert into `qu...', Array, Object(Closure))
#1 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\MySqlConnection.php(42): Illuminate\\Database\\Connection->run('insert into `qu...', Array, Object(Closure))
#2 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Processors\\MySqlProcessor.php(35): Illuminate\\Database\\MySqlConnection->insert('insert into `qu...', Array, 'id')
#3 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3796): Illuminate\\Database\\Query\\Processors\\MySqlProcessor->processInsertGetId(Object(Illuminate\\Database\\Query\\Builder), 'insert into `qu...', Array, 'id')
#4 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(2205): Illuminate\\Database\\Query\\Builder->insertGetId(Array, 'id')
#5 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1408): Illuminate\\Database\\Eloquent\\Builder->__call('insertGetId', Array)
#6 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1373): Illuminate\\Database\\Eloquent\\Model->insertAndSetId(Object(Illuminate\\Database\\Eloquent\\Builder), Array)
#7 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1212): Illuminate\\Database\\Eloquent\\Model->performInsert(Object(Illuminate\\Database\\Eloquent\\Builder))
#8 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(1189): Illuminate\\Database\\Eloquent\\Model->save()
#9 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php(399): Illuminate\\Database\\Eloquent\\Builder->Illuminate\\Database\\Eloquent\\{closure}(Object(App\\Models\\Quote))
#10 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(1188): tap(Object(App\\Models\\Quote), Object(Closure))
#11 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Traits\\ForwardsCalls.php(23): Illuminate\\Database\\Eloquent\\Builder->create(Array)
#12 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(2449): Illuminate\\Database\\Eloquent\\Model->forwardCallTo(Object(Illuminate\\Database\\Eloquent\\Builder), 'create', Array)
#13 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(2465): Illuminate\\Database\\Eloquent\\Model->__call('create', Array)
#14 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\app\\Http\\Controllers\\QuoteController.php(128): Illuminate\\Database\\Eloquent\\Model::__callStatic('create', Array)
#15 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(46): App\\Http\\Controllers\\QuoteController->store(Object(Illuminate\\Http\\Request))
#16 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(265): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\QuoteController), 'store')
#17 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(211): Illuminate\\Routing\\Route->runController()
#18 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(808): Illuminate\\Routing\\Route->run()
#19 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#20 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\app\\Http\\Middleware\\LocaleMiddleware.php(44): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#21 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): App\\Http\\Middleware\\LocaleMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#22 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#23 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#24 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(87): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#25 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#26 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#27 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#28 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(120): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#29 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(63): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#30 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(36): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(74): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#34 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#35 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#36 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#37 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#38 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#39 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#40 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#41 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#42 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#43 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#44 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#45 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#46 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#47 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#48 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#49 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#50 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#51 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#52 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#53 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#54 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#55 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#56 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#57 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#58 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#59 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#60 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#61 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#62 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#63 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1219): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#64 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#65 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('C:\\\\Users\\\\<USER>\\\\...')
#66 {main}

[previous exception] [object] (PDOException(code: 23000): SQLSTATE[23000]: Integrity constraint violation: 1048 Column 'description' cannot be null at C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\MySqlConnection.php:53)
[stacktrace]
#0 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\MySqlConnection.php(53): PDOStatement->execute()
#1 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(809): Illuminate\\Database\\MySqlConnection->Illuminate\\Database\\{closure}('insert into `qu...', Array)
#2 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback('insert into `qu...', Array, Object(Closure))
#3 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\MySqlConnection.php(42): Illuminate\\Database\\Connection->run('insert into `qu...', Array, Object(Closure))
#4 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Processors\\MySqlProcessor.php(35): Illuminate\\Database\\MySqlConnection->insert('insert into `qu...', Array, 'id')
#5 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3796): Illuminate\\Database\\Query\\Processors\\MySqlProcessor->processInsertGetId(Object(Illuminate\\Database\\Query\\Builder), 'insert into `qu...', Array, 'id')
#6 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(2205): Illuminate\\Database\\Query\\Builder->insertGetId(Array, 'id')
#7 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1408): Illuminate\\Database\\Eloquent\\Builder->__call('insertGetId', Array)
#8 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1373): Illuminate\\Database\\Eloquent\\Model->insertAndSetId(Object(Illuminate\\Database\\Eloquent\\Builder), Array)
#9 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1212): Illuminate\\Database\\Eloquent\\Model->performInsert(Object(Illuminate\\Database\\Eloquent\\Builder))
#10 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(1189): Illuminate\\Database\\Eloquent\\Model->save()
#11 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php(399): Illuminate\\Database\\Eloquent\\Builder->Illuminate\\Database\\Eloquent\\{closure}(Object(App\\Models\\Quote))
#12 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(1188): tap(Object(App\\Models\\Quote), Object(Closure))
#13 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Traits\\ForwardsCalls.php(23): Illuminate\\Database\\Eloquent\\Builder->create(Array)
#14 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(2449): Illuminate\\Database\\Eloquent\\Model->forwardCallTo(Object(Illuminate\\Database\\Eloquent\\Builder), 'create', Array)
#15 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(2465): Illuminate\\Database\\Eloquent\\Model->__call('create', Array)
#16 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\app\\Http\\Controllers\\QuoteController.php(128): Illuminate\\Database\\Eloquent\\Model::__callStatic('create', Array)
#17 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(46): App\\Http\\Controllers\\QuoteController->store(Object(Illuminate\\Http\\Request))
#18 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(265): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\QuoteController), 'store')
#19 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(211): Illuminate\\Routing\\Route->runController()
#20 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(808): Illuminate\\Routing\\Route->run()
#21 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#22 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\app\\Http\\Middleware\\LocaleMiddleware.php(44): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#23 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): App\\Http\\Middleware\\LocaleMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#24 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#25 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#26 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(87): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#27 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#28 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#29 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(120): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#31 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(63): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#32 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(36): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#34 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#35 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(74): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#36 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#37 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#38 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#39 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#40 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#41 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#42 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#43 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#44 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#45 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#46 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#47 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#48 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#49 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#50 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#51 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#52 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#53 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#54 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#55 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#56 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#57 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#58 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#59 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#60 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#61 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#62 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#63 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#64 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#65 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1219): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#66 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#67 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('C:\\\\Users\\\\<USER>\\\\...')
#68 {main}
"} 
[2025-06-09 17:46:26] local.INFO: Quote store method called {"request_data":{"_token":"u5rXKfgKs7BxiYovzdp4zUYFtiLkddiJtM0RnRoQ","quote_type":"shipping","service_type":"international_shipping","quote_source":"shipping_tab","customer_name":"name","customer_email":"<EMAIL>","customer_phone":"6546454","company_name":"lksjd","priority":"standard","description":null,"origin_address":"asdasd","origin_city":"asdad","destination_address":"adsada","destination_city":"asdadad"}} 
[2025-06-09 17:46:26] local.ERROR: SQLSTATE[23000]: Integrity constraint violation: 1048 Column 'description' cannot be null (Connection: mysql, SQL: insert into `quotes` (`quote_type`, `customer_name`, `customer_email`, `customer_phone`, `company_name`, `priority`, `description`, `service_type`, `origin_address`, `origin_city`, `destination_address`, `destination_city`, `quote_source`, `fragile`, `hazardous`, `insurance_required`, `signature_required`, `status`, `quote_number`, `updated_at`, `created_at`) values (shipping, name, <EMAIL>, 6546454, lksjd, standard, ?, international_shipping, asdasd, asdad, adsada, asdadad, shipping_tab, 0, 0, 0, 0, pending, QTE202506097639, 2025-06-09 17:46:26, 2025-06-09 17:46:26)) {"exception":"[object] (Illuminate\\Database\\QueryException(code: 23000): SQLSTATE[23000]: Integrity constraint violation: 1048 Column 'description' cannot be null (Connection: mysql, SQL: insert into `quotes` (`quote_type`, `customer_name`, `customer_email`, `customer_phone`, `company_name`, `priority`, `description`, `service_type`, `origin_address`, `origin_city`, `destination_address`, `destination_city`, `quote_source`, `fragile`, `hazardous`, `insurance_required`, `signature_required`, `status`, `quote_number`, `updated_at`, `created_at`) values (shipping, name, <EMAIL>, 6546454, lksjd, standard, ?, international_shipping, asdasd, asdad, adsada, asdadad, shipping_tab, 0, 0, 0, 0, pending, QTE202506097639, 2025-06-09 17:46:26, 2025-06-09 17:46:26)) at C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:822)
[stacktrace]
#0 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback('insert into `qu...', Array, Object(Closure))
#1 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\MySqlConnection.php(42): Illuminate\\Database\\Connection->run('insert into `qu...', Array, Object(Closure))
#2 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Processors\\MySqlProcessor.php(35): Illuminate\\Database\\MySqlConnection->insert('insert into `qu...', Array, 'id')
#3 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3796): Illuminate\\Database\\Query\\Processors\\MySqlProcessor->processInsertGetId(Object(Illuminate\\Database\\Query\\Builder), 'insert into `qu...', Array, 'id')
#4 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(2205): Illuminate\\Database\\Query\\Builder->insertGetId(Array, 'id')
#5 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1408): Illuminate\\Database\\Eloquent\\Builder->__call('insertGetId', Array)
#6 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1373): Illuminate\\Database\\Eloquent\\Model->insertAndSetId(Object(Illuminate\\Database\\Eloquent\\Builder), Array)
#7 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1212): Illuminate\\Database\\Eloquent\\Model->performInsert(Object(Illuminate\\Database\\Eloquent\\Builder))
#8 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(1189): Illuminate\\Database\\Eloquent\\Model->save()
#9 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php(399): Illuminate\\Database\\Eloquent\\Builder->Illuminate\\Database\\Eloquent\\{closure}(Object(App\\Models\\Quote))
#10 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(1188): tap(Object(App\\Models\\Quote), Object(Closure))
#11 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Traits\\ForwardsCalls.php(23): Illuminate\\Database\\Eloquent\\Builder->create(Array)
#12 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(2449): Illuminate\\Database\\Eloquent\\Model->forwardCallTo(Object(Illuminate\\Database\\Eloquent\\Builder), 'create', Array)
#13 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(2465): Illuminate\\Database\\Eloquent\\Model->__call('create', Array)
#14 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\app\\Http\\Controllers\\QuoteController.php(128): Illuminate\\Database\\Eloquent\\Model::__callStatic('create', Array)
#15 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(46): App\\Http\\Controllers\\QuoteController->store(Object(Illuminate\\Http\\Request))
#16 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(265): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\QuoteController), 'store')
#17 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(211): Illuminate\\Routing\\Route->runController()
#18 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(808): Illuminate\\Routing\\Route->run()
#19 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#20 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\app\\Http\\Middleware\\LocaleMiddleware.php(44): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#21 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): App\\Http\\Middleware\\LocaleMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#22 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#23 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#24 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(87): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#25 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#26 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#27 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#28 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(120): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#29 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(63): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#30 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(36): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(74): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#34 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#35 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#36 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#37 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#38 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#39 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#40 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#41 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#42 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#43 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#44 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#45 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#46 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#47 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#48 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#49 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#50 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#51 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#52 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#53 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#54 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#55 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#56 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#57 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#58 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#59 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#60 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#61 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#62 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#63 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1219): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#64 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#65 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('C:\\\\Users\\\\<USER>\\\\...')
#66 {main}

[previous exception] [object] (PDOException(code: 23000): SQLSTATE[23000]: Integrity constraint violation: 1048 Column 'description' cannot be null at C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\MySqlConnection.php:53)
[stacktrace]
#0 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\MySqlConnection.php(53): PDOStatement->execute()
#1 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(809): Illuminate\\Database\\MySqlConnection->Illuminate\\Database\\{closure}('insert into `qu...', Array)
#2 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback('insert into `qu...', Array, Object(Closure))
#3 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\MySqlConnection.php(42): Illuminate\\Database\\Connection->run('insert into `qu...', Array, Object(Closure))
#4 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Processors\\MySqlProcessor.php(35): Illuminate\\Database\\MySqlConnection->insert('insert into `qu...', Array, 'id')
#5 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3796): Illuminate\\Database\\Query\\Processors\\MySqlProcessor->processInsertGetId(Object(Illuminate\\Database\\Query\\Builder), 'insert into `qu...', Array, 'id')
#6 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(2205): Illuminate\\Database\\Query\\Builder->insertGetId(Array, 'id')
#7 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1408): Illuminate\\Database\\Eloquent\\Builder->__call('insertGetId', Array)
#8 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1373): Illuminate\\Database\\Eloquent\\Model->insertAndSetId(Object(Illuminate\\Database\\Eloquent\\Builder), Array)
#9 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1212): Illuminate\\Database\\Eloquent\\Model->performInsert(Object(Illuminate\\Database\\Eloquent\\Builder))
#10 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(1189): Illuminate\\Database\\Eloquent\\Model->save()
#11 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php(399): Illuminate\\Database\\Eloquent\\Builder->Illuminate\\Database\\Eloquent\\{closure}(Object(App\\Models\\Quote))
#12 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(1188): tap(Object(App\\Models\\Quote), Object(Closure))
#13 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Traits\\ForwardsCalls.php(23): Illuminate\\Database\\Eloquent\\Builder->create(Array)
#14 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(2449): Illuminate\\Database\\Eloquent\\Model->forwardCallTo(Object(Illuminate\\Database\\Eloquent\\Builder), 'create', Array)
#15 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(2465): Illuminate\\Database\\Eloquent\\Model->__call('create', Array)
#16 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\app\\Http\\Controllers\\QuoteController.php(128): Illuminate\\Database\\Eloquent\\Model::__callStatic('create', Array)
#17 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(46): App\\Http\\Controllers\\QuoteController->store(Object(Illuminate\\Http\\Request))
#18 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(265): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\QuoteController), 'store')
#19 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(211): Illuminate\\Routing\\Route->runController()
#20 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(808): Illuminate\\Routing\\Route->run()
#21 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#22 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\app\\Http\\Middleware\\LocaleMiddleware.php(44): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#23 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): App\\Http\\Middleware\\LocaleMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#24 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#25 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#26 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(87): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#27 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#28 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#29 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(120): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#31 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(63): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#32 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(36): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#34 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#35 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(74): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#36 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#37 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#38 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#39 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#40 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#41 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#42 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#43 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#44 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#45 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#46 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#47 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#48 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#49 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#50 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#51 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#52 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#53 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#54 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#55 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#56 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#57 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#58 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#59 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#60 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#61 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#62 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#63 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#64 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#65 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1219): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#66 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#67 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('C:\\\\Users\\\\<USER>\\\\...')
#68 {main}
"} 
[2025-06-09 17:47:59] local.INFO: Quote store method called {"request_data":{"_token":"1Y6d4pn6UMdQVMOjg7fDgFR1ULTwuM0o9poSW2c4","quote_type":"shipping","service_type":"international_shipping","quote_source":"shipping_tab","customer_name":"aslkj","customer_email":"<EMAIL>","customer_phone":"kjklajs","company_name":"kljxksj","priority":"standard","description":null,"origin_address":"kljsdkj","origin_city":"dsjklsd","destination_address":"ksdjkl","destination_city":"dskjkljs"}} 
[2025-06-09 17:47:59] local.ERROR: SQLSTATE[23000]: Integrity constraint violation: 1048 Column 'description' cannot be null (Connection: mysql, SQL: insert into `quotes` (`quote_type`, `customer_name`, `customer_email`, `customer_phone`, `company_name`, `priority`, `description`, `service_type`, `origin_address`, `origin_city`, `destination_address`, `destination_city`, `quote_source`, `fragile`, `hazardous`, `insurance_required`, `signature_required`, `status`, `quote_number`, `updated_at`, `created_at`) values (shipping, aslkj, <EMAIL>, kjklajs, kljxksj, standard, ?, international_shipping, kljsdkj, dsjklsd, ksdjkl, dskjkljs, shipping_tab, 0, 0, 0, 0, pending, QTE202506097999, 2025-06-09 17:47:59, 2025-06-09 17:47:59)) {"exception":"[object] (Illuminate\\Database\\QueryException(code: 23000): SQLSTATE[23000]: Integrity constraint violation: 1048 Column 'description' cannot be null (Connection: mysql, SQL: insert into `quotes` (`quote_type`, `customer_name`, `customer_email`, `customer_phone`, `company_name`, `priority`, `description`, `service_type`, `origin_address`, `origin_city`, `destination_address`, `destination_city`, `quote_source`, `fragile`, `hazardous`, `insurance_required`, `signature_required`, `status`, `quote_number`, `updated_at`, `created_at`) values (shipping, aslkj, <EMAIL>, kjklajs, kljxksj, standard, ?, international_shipping, kljsdkj, dsjklsd, ksdjkl, dskjkljs, shipping_tab, 0, 0, 0, 0, pending, QTE202506097999, 2025-06-09 17:47:59, 2025-06-09 17:47:59)) at C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:822)
[stacktrace]
#0 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback('insert into `qu...', Array, Object(Closure))
#1 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\MySqlConnection.php(42): Illuminate\\Database\\Connection->run('insert into `qu...', Array, Object(Closure))
#2 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Processors\\MySqlProcessor.php(35): Illuminate\\Database\\MySqlConnection->insert('insert into `qu...', Array, 'id')
#3 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3796): Illuminate\\Database\\Query\\Processors\\MySqlProcessor->processInsertGetId(Object(Illuminate\\Database\\Query\\Builder), 'insert into `qu...', Array, 'id')
#4 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(2205): Illuminate\\Database\\Query\\Builder->insertGetId(Array, 'id')
#5 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1408): Illuminate\\Database\\Eloquent\\Builder->__call('insertGetId', Array)
#6 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1373): Illuminate\\Database\\Eloquent\\Model->insertAndSetId(Object(Illuminate\\Database\\Eloquent\\Builder), Array)
#7 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1212): Illuminate\\Database\\Eloquent\\Model->performInsert(Object(Illuminate\\Database\\Eloquent\\Builder))
#8 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(1189): Illuminate\\Database\\Eloquent\\Model->save()
#9 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php(399): Illuminate\\Database\\Eloquent\\Builder->Illuminate\\Database\\Eloquent\\{closure}(Object(App\\Models\\Quote))
#10 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(1188): tap(Object(App\\Models\\Quote), Object(Closure))
#11 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Traits\\ForwardsCalls.php(23): Illuminate\\Database\\Eloquent\\Builder->create(Array)
#12 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(2449): Illuminate\\Database\\Eloquent\\Model->forwardCallTo(Object(Illuminate\\Database\\Eloquent\\Builder), 'create', Array)
#13 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(2465): Illuminate\\Database\\Eloquent\\Model->__call('create', Array)
#14 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\app\\Http\\Controllers\\QuoteController.php(128): Illuminate\\Database\\Eloquent\\Model::__callStatic('create', Array)
#15 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(46): App\\Http\\Controllers\\QuoteController->store(Object(Illuminate\\Http\\Request))
#16 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(265): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\QuoteController), 'store')
#17 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(211): Illuminate\\Routing\\Route->runController()
#18 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(808): Illuminate\\Routing\\Route->run()
#19 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#20 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\app\\Http\\Middleware\\LocaleMiddleware.php(44): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#21 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): App\\Http\\Middleware\\LocaleMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#22 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#23 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#24 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(87): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#25 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#26 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#27 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#28 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(120): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#29 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(63): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#30 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(36): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(74): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#34 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#35 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#36 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#37 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#38 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#39 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#40 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#41 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#42 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#43 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#44 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#45 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#46 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#47 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#48 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#49 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#50 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#51 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#52 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#53 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#54 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#55 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#56 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#57 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#58 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#59 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#60 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#61 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#62 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#63 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1219): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#64 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#65 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('C:\\\\Users\\\\<USER>\\\\...')
#66 {main}

[previous exception] [object] (PDOException(code: 23000): SQLSTATE[23000]: Integrity constraint violation: 1048 Column 'description' cannot be null at C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\MySqlConnection.php:53)
[stacktrace]
#0 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\MySqlConnection.php(53): PDOStatement->execute()
#1 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(809): Illuminate\\Database\\MySqlConnection->Illuminate\\Database\\{closure}('insert into `qu...', Array)
#2 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(776): Illuminate\\Database\\Connection->runQueryCallback('insert into `qu...', Array, Object(Closure))
#3 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\MySqlConnection.php(42): Illuminate\\Database\\Connection->run('insert into `qu...', Array, Object(Closure))
#4 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Processors\\MySqlProcessor.php(35): Illuminate\\Database\\MySqlConnection->insert('insert into `qu...', Array, 'id')
#5 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3796): Illuminate\\Database\\Query\\Processors\\MySqlProcessor->processInsertGetId(Object(Illuminate\\Database\\Query\\Builder), 'insert into `qu...', Array, 'id')
#6 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(2205): Illuminate\\Database\\Query\\Builder->insertGetId(Array, 'id')
#7 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1408): Illuminate\\Database\\Eloquent\\Builder->__call('insertGetId', Array)
#8 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1373): Illuminate\\Database\\Eloquent\\Model->insertAndSetId(Object(Illuminate\\Database\\Eloquent\\Builder), Array)
#9 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1212): Illuminate\\Database\\Eloquent\\Model->performInsert(Object(Illuminate\\Database\\Eloquent\\Builder))
#10 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(1189): Illuminate\\Database\\Eloquent\\Model->save()
#11 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php(399): Illuminate\\Database\\Eloquent\\Builder->Illuminate\\Database\\Eloquent\\{closure}(Object(App\\Models\\Quote))
#12 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(1188): tap(Object(App\\Models\\Quote), Object(Closure))
#13 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Traits\\ForwardsCalls.php(23): Illuminate\\Database\\Eloquent\\Builder->create(Array)
#14 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(2449): Illuminate\\Database\\Eloquent\\Model->forwardCallTo(Object(Illuminate\\Database\\Eloquent\\Builder), 'create', Array)
#15 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(2465): Illuminate\\Database\\Eloquent\\Model->__call('create', Array)
#16 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\app\\Http\\Controllers\\QuoteController.php(128): Illuminate\\Database\\Eloquent\\Model::__callStatic('create', Array)
#17 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(46): App\\Http\\Controllers\\QuoteController->store(Object(Illuminate\\Http\\Request))
#18 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(265): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\QuoteController), 'store')
#19 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(211): Illuminate\\Routing\\Route->runController()
#20 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(808): Illuminate\\Routing\\Route->run()
#21 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#22 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\app\\Http\\Middleware\\LocaleMiddleware.php(44): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#23 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): App\\Http\\Middleware\\LocaleMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#24 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#25 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#26 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(87): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#27 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#28 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#29 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(120): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#31 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(63): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#32 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(36): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#34 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#35 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(74): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#36 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#37 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#38 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#39 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#40 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#41 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#42 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#43 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#44 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#45 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#46 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#47 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#48 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#49 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#50 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#51 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#52 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#53 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#54 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#55 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#56 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#57 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#58 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#59 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#60 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#61 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#62 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#63 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#64 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#65 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1219): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#66 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#67 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('C:\\\\Users\\\\<USER>\\\\...')
#68 {main}
"} 
[2025-06-09 18:04:07] local.ERROR: View [admin.quotes.edit] not found. {"userId":1,"exception":"[object] (InvalidArgumentException(code: 0): View [admin.quotes.edit] not found. at C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\View\\FileViewFinder.php:138)
[stacktrace]
#0 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\View\\FileViewFinder.php(78): Illuminate\\View\\FileViewFinder->findInPaths('admin.quotes.ed...', Array)
#1 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Factory.php(150): Illuminate\\View\\FileViewFinder->find('admin.quotes.ed...')
#2 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php(1079): Illuminate\\View\\Factory->make('admin.quotes.ed...', Array, Array)
#3 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\app\\Http\\Controllers\\Admin\\QuoteController.php(237): view('admin.quotes.ed...', Array)
#4 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(46): App\\Http\\Controllers\\Admin\\QuoteController->edit(Object(App\\Models\\Quote))
#5 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(265): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\Admin\\QuoteController), 'edit')
#6 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(211): Illuminate\\Routing\\Route->runController()
#7 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(808): Illuminate\\Routing\\Route->run()
#8 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#9 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\app\\Http\\Middleware\\AdminMiddleware.php(28): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#10 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): App\\Http\\Middleware\\AdminMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#11 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\app\\Http\\Middleware\\LocaleMiddleware.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#12 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): App\\Http\\Middleware\\LocaleMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#13 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#14 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#15 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(63): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#16 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#17 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(87): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#18 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#19 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#20 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#21 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(120): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#22 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(63): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#23 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#24 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(36): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#25 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#26 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(74): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#27 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#28 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#29 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#30 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#31 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#32 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#33 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#34 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#35 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#36 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#37 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#38 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#39 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#40 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#41 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#42 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#43 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#44 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#45 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#46 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#47 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#48 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#49 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#50 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#51 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#52 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#53 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#54 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#55 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#56 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1219): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#57 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#58 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('C:\\\\Users\\\\<USER>\\\\...')
#59 {main}
"} 
[2025-06-09 18:05:22] local.ERROR: View [admin.quotes.edit] not found. {"userId":1,"exception":"[object] (InvalidArgumentException(code: 0): View [admin.quotes.edit] not found. at C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\View\\FileViewFinder.php:138)
[stacktrace]
#0 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\View\\FileViewFinder.php(78): Illuminate\\View\\FileViewFinder->findInPaths('admin.quotes.ed...', Array)
#1 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Factory.php(150): Illuminate\\View\\FileViewFinder->find('admin.quotes.ed...')
#2 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php(1079): Illuminate\\View\\Factory->make('admin.quotes.ed...', Array, Array)
#3 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\app\\Http\\Controllers\\Admin\\QuoteController.php(237): view('admin.quotes.ed...', Array)
#4 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(46): App\\Http\\Controllers\\Admin\\QuoteController->edit(Object(App\\Models\\Quote))
#5 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(265): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\Admin\\QuoteController), 'edit')
#6 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(211): Illuminate\\Routing\\Route->runController()
#7 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(808): Illuminate\\Routing\\Route->run()
#8 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#9 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\app\\Http\\Middleware\\AdminMiddleware.php(28): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#10 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): App\\Http\\Middleware\\AdminMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#11 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\app\\Http\\Middleware\\LocaleMiddleware.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#12 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): App\\Http\\Middleware\\LocaleMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#13 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#14 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#15 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(63): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#16 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#17 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(87): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#18 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#19 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#20 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#21 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(120): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#22 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(63): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#23 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#24 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(36): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#25 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#26 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(74): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#27 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#28 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#29 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#30 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#31 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#32 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#33 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#34 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#35 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#36 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#37 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#38 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#39 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#40 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#41 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#42 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#43 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#44 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#45 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#46 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#47 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#48 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#49 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#50 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#51 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#52 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#53 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#54 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#55 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#56 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1219): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#57 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#58 C:\\Users\\<USER>\\Desktop\\Chisolution Inc\\Logistics\\Atrix\\atrix-logistics\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('C:\\\\Users\\\\<USER>\\\\...')
#59 {main}
"} 
[2025-06-09 18:45:04] local.ERROR: Failed to send contact notification email: Connection could not be established with host "127.0.0.1:1025": stream_socket_client(): Unable to connect to 127.0.0.1:1025 (No connection could be made because the target machine actively refused it)  
