<?php $__env->startSection('title', 'Contact Us - ' . ($siteSettings['site_name'] ?? 'Atrix Logistics')); ?>
<?php $__env->startSection('description', 'Get in touch with our logistics experts. Contact us for quotes, support, or any questions about our shipping services.'); ?>

<?php $__env->startSection('content'); ?>

<!-- Hero Section with Animated Background -->
<section class="relative min-h-[60vh] flex items-center justify-center overflow-hidden bg-gradient-to-br from-gray-900 via-gray-800 to-green-900">
    <!-- Animated Background Elements -->
    <div class="absolute inset-0">
        <!-- Floating Particles -->
        <div class="absolute w-2 h-2 bg-green-400 rounded-full animate-float" style="top: 20%; left: 10%; animation-delay: 0s;"></div>
        <div class="absolute w-3 h-3 bg-blue-400 rounded-full animate-float" style="top: 60%; left: 80%; animation-delay: 2s;"></div>
        <div class="absolute w-1 h-1 bg-white rounded-full animate-float" style="top: 40%; left: 20%; animation-delay: 1s;"></div>
        <div class="absolute w-2 h-2 bg-green-300 rounded-full animate-float" style="top: 80%; left: 60%; animation-delay: 3s;"></div>
        <div class="absolute w-1 h-1 bg-blue-300 rounded-full animate-float" style="top: 30%; left: 90%; animation-delay: 1.5s;"></div>

        <!-- Gradient Orbs -->
        <div class="absolute top-1/4 left-1/4 w-64 h-64 bg-gradient-to-r from-green-400/20 to-blue-400/20 rounded-full blur-3xl animate-pulse"></div>
        <div class="absolute bottom-1/4 right-1/4 w-96 h-96 bg-gradient-to-r from-blue-400/10 to-green-400/10 rounded-full blur-3xl animate-pulse" style="animation-delay: 1s;"></div>
    </div>

    <!-- Content -->
    <div class="container mx-auto px-4 relative z-10">
        <div class="text-center max-w-4xl mx-auto">
            <div class="animate-on-scroll">
                <h1 class="text-5xl lg:text-7xl font-bold font-heading text-white mb-6">
                    Get In <span class="text-transparent bg-clip-text bg-gradient-to-r from-green-400 to-blue-400">Touch</span>
                </h1>
                <p class="text-xl lg:text-2xl text-gray-300 mb-8">
                    Ready to transform your logistics? Let's start the conversation.
                </p>

                <!-- Breadcrumb -->
                <nav class="flex justify-center items-center space-x-2 text-gray-400">
                    <a href="<?php echo e(route('home')); ?>" class="hover:text-green-400 transition-colors">Home</a>
                    <i class="fas fa-chevron-right text-xs"></i>
                    <span class="text-green-400">Contact</span>
                </nav>
            </div>
        </div>
    </div>

    <!-- Scroll Indicator -->
    <div class="absolute bottom-8 left-1/2 transform -translate-x-1/2 animate-bounce">
        <div class="w-6 h-10 border-2 border-white/30 rounded-full flex justify-center">
            <div class="w-1 h-3 bg-white/60 rounded-full mt-2 animate-pulse"></div>
        </div>
    </div>
</section>

<!-- Contact Information Cards -->
<section class="py-20 bg-gray-50 relative overflow-hidden">
    <!-- Background Pattern -->
    <div class="absolute inset-0 opacity-5">
        <div class="absolute inset-0" style="background-image: url('data:image/svg+xml,<svg width="60" height="60" viewBox="0 0 60 60" xmlns="http://www.w3.org/2000/svg"><g fill="none" fill-rule="evenodd"><g fill="%23000000" fill-opacity="0.1"><circle cx="30" cy="30" r="2"/></g></svg>'); background-size: 60px 60px;"></div>
    </div>

    <div class="container mx-auto px-4 relative z-10">
        <!-- Section Header -->
        <div class="text-center mb-16 animate-on-scroll">
            <h2 class="text-4xl lg:text-5xl font-bold font-heading text-gray-900 mb-6">
                Multiple Ways to <span class="text-transparent bg-clip-text bg-gradient-to-r from-green-600 to-blue-600">Connect</span>
            </h2>
            <p class="text-xl text-gray-600 max-w-3xl mx-auto">
                Choose your preferred method to reach our logistics experts. We're here to help 24/7.
            </p>
        </div>

        <!-- Contact Cards -->
        <div class="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-6xl mx-auto">
            <!-- Phone Card -->
            <div class="group animate-on-scroll">
                <div class="bg-white rounded-2xl p-8 shadow-lg hover:shadow-2xl transition-all duration-500 transform hover:-translate-y-2 border border-gray-100 relative overflow-hidden">
                    <!-- Card Background Effect -->
                    <div class="absolute inset-0 bg-gradient-to-br from-green-50 to-blue-50 opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>

                    <!-- Content -->
                    <div class="relative z-10 text-center">
                        <!-- Icon -->
                        <div class="w-20 h-20 mx-auto mb-6 bg-gradient-to-br from-green-500 to-green-600 rounded-2xl flex items-center justify-center shadow-lg group-hover:scale-110 transition-transform duration-300">
                            <i class="fas fa-phone text-2xl text-white"></i>
                        </div>

                        <!-- Title -->
                        <h3 class="text-2xl font-bold text-gray-900 mb-3 group-hover:text-green-600 transition-colors">Phone Support</h3>
                        <p class="text-gray-600 mb-6">Call us for immediate assistance and expert guidance</p>

                        <!-- Contact Info -->
                        <div class="space-y-2">
                            <?php if(!empty($siteSettings['phone'])): ?>
                            <a href="tel:<?php echo e($siteSettings['phone']); ?>" class="block text-lg font-semibold text-green-600 hover:text-green-700 transition-colors">
                                <?php echo e($siteSettings['phone']); ?>

                            </a>
                            <?php endif; ?>
                            <?php if(!empty($siteSettings['phone_2'])): ?>
                            <a href="tel:<?php echo e($siteSettings['phone_2']); ?>" class="block text-gray-700 hover:text-green-600 transition-colors">
                                <?php echo e($siteSettings['phone_2']); ?>

                            </a>
                            <?php endif; ?>
                        </div>

                        <!-- Call Button -->
                        <div class="mt-6">
                            <a href="tel:<?php echo e($siteSettings['phone'] ?? ''); ?>" class="inline-flex items-center px-6 py-3 bg-green-600 hover:bg-green-700 text-white rounded-lg font-semibold transition-colors">
                                <i class="fas fa-phone mr-2"></i>
                                Call Now
                            </a>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Email Card -->
            <div class="group animate-on-scroll">
                <div class="bg-white rounded-2xl p-8 shadow-lg hover:shadow-2xl transition-all duration-500 transform hover:-translate-y-2 border border-gray-100 relative overflow-hidden">
                    <!-- Card Background Effect -->
                    <div class="absolute inset-0 bg-gradient-to-br from-blue-50 to-green-50 opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>

                    <!-- Content -->
                    <div class="relative z-10 text-center">
                        <!-- Icon -->
                        <div class="w-20 h-20 mx-auto mb-6 bg-gradient-to-br from-blue-500 to-blue-600 rounded-2xl flex items-center justify-center shadow-lg group-hover:scale-110 transition-transform duration-300">
                            <i class="fas fa-envelope text-2xl text-white"></i>
                        </div>

                        <!-- Title -->
                        <h3 class="text-2xl font-bold text-gray-900 mb-3 group-hover:text-blue-600 transition-colors">Email Us</h3>
                        <p class="text-gray-600 mb-6">Send us detailed inquiries and get comprehensive responses</p>

                        <!-- Contact Info -->
                        <div class="space-y-2">
                            <?php if(!empty($siteSettings['email'])): ?>
                            <a href="mailto:<?php echo e($siteSettings['email']); ?>" class="block text-lg font-semibold text-blue-600 hover:text-blue-700 transition-colors">
                                <?php echo e($siteSettings['email']); ?>

                            </a>
                            <?php endif; ?>
                            <?php if(!empty($siteSettings['support_email'])): ?>
                            <a href="mailto:<?php echo e($siteSettings['support_email']); ?>" class="block text-gray-700 hover:text-blue-600 transition-colors">
                                <?php echo e($siteSettings['support_email']); ?>

                            </a>
                            <?php endif; ?>
                        </div>

                        <!-- Email Button -->
                        <div class="mt-6">
                            <a href="mailto:<?php echo e($siteSettings['email'] ?? ''); ?>" class="inline-flex items-center px-6 py-3 bg-blue-600 hover:bg-blue-700 text-white rounded-lg font-semibold transition-colors">
                                <i class="fas fa-envelope mr-2"></i>
                                Send Email
                            </a>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Location Card -->
            <div class="group animate-on-scroll">
                <div class="bg-white rounded-2xl p-8 shadow-lg hover:shadow-2xl transition-all duration-500 transform hover:-translate-y-2 border border-gray-100 relative overflow-hidden">
                    <!-- Card Background Effect -->
                    <div class="absolute inset-0 bg-gradient-to-br from-gray-50 to-green-50 opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>

                    <!-- Content -->
                    <div class="relative z-10 text-center">
                        <!-- Icon -->
                        <div class="w-20 h-20 mx-auto mb-6 bg-gradient-to-br from-gray-600 to-gray-700 rounded-2xl flex items-center justify-center shadow-lg group-hover:scale-110 transition-transform duration-300">
                            <i class="fas fa-map-marker-alt text-2xl text-white"></i>
                        </div>

                        <!-- Title -->
                        <h3 class="text-2xl font-bold text-gray-900 mb-3 group-hover:text-gray-700 transition-colors">Visit Our Office</h3>
                        <p class="text-gray-600 mb-6">Come see our operations and meet our team in person</p>

                        <!-- Contact Info -->
                        <div class="text-gray-700 leading-relaxed mb-6">
                            <?php echo e($siteSettings['address'] ?? 'Flat 20, Reynolds Neck, North Helenaville, FV77 8WS'); ?>

                        </div>

                        <!-- Directions Button -->
                        <div class="mt-6">
                            <a href="#map" class="inline-flex items-center px-6 py-3 bg-gray-600 hover:bg-gray-700 text-white rounded-lg font-semibold transition-colors">
                                <i class="fas fa-directions mr-2"></i>
                                Get Directions
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Contact Form Section -->
<section class="py-20 bg-white relative overflow-hidden">
    <!-- Background Elements -->
    <div class="absolute inset-0">
        <!-- Gradient Mesh -->
        <div class="absolute top-0 left-0 w-full h-full opacity-30">
            <div class="absolute top-1/4 left-1/4 w-96 h-96 bg-gradient-to-r from-green-200/30 to-blue-200/30 rounded-full blur-3xl"></div>
            <div class="absolute bottom-1/4 right-1/4 w-80 h-80 bg-gradient-to-r from-blue-200/20 to-green-200/20 rounded-full blur-3xl"></div>
        </div>
    </div>

    <div class="container mx-auto px-4 relative z-10">
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-12 max-w-7xl mx-auto">
            <!-- Contact Form -->
            <div class="lg:col-span-2">
                <div class="animate-on-scroll">
                    <!-- Form Header -->
                    <div class="mb-12">
                        <h2 class="text-4xl lg:text-5xl font-bold font-heading text-gray-900 mb-6">
                            Send Us a <span class="text-transparent bg-clip-text bg-gradient-to-r from-green-600 to-blue-600">Message</span>
                        </h2>
                        <p class="text-xl text-gray-600">
                            Ready to discuss your logistics needs? Fill out the form below and our experts will get back to you within 24 hours.
                        </p>
                    </div>

                    <!-- Success/Error Messages -->
                    <?php if(session('success')): ?>
                    <div class="bg-green-50 border border-green-200 rounded-2xl p-6 mb-8 animate-on-scroll">
                        <div class="flex items-center">
                            <div class="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center mr-4">
                                <i class="fas fa-check text-green-600 text-xl"></i>
                            </div>
                            <div>
                                <h3 class="text-green-800 font-semibold text-lg">Message Sent Successfully!</h3>
                                <p class="text-green-700"><?php echo e(session('success')); ?></p>
                            </div>
                        </div>
                    </div>
                    <?php endif; ?>

                    <?php if($errors->any()): ?>
                    <div class="bg-red-50 border border-red-200 rounded-2xl p-6 mb-8 animate-on-scroll">
                        <div class="flex items-start">
                            <div class="w-12 h-12 bg-red-100 rounded-full flex items-center justify-center mr-4 mt-1">
                                <i class="fas fa-exclamation-triangle text-red-600 text-xl"></i>
                            </div>
                            <div>
                                <h3 class="text-red-800 font-semibold text-lg mb-2">Please fix the following errors:</h3>
                                <ul class="text-red-700 space-y-1">
                                    <?php $__currentLoopData = $errors->all(); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $error): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <li class="flex items-center">
                                        <i class="fas fa-circle text-xs mr-2"></i>
                                        <?php echo e($error); ?>

                                    </li>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </ul>
                            </div>
                        </div>
                    </div>
                    <?php endif; ?>

                    <!-- Contact Form -->
                    <form method="post" action="<?php echo e(route('contact.submit')); ?>" id="contact-form" class="space-y-6">
                        <?php echo csrf_field(); ?>

                        <!-- Name and Email Row -->
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div class="group">
                                <label for="name" class="block text-sm font-semibold text-gray-700 mb-2">Full Name *</label>
                                <div class="relative">
                                    <input type="text" id="name" name="name" required value="<?php echo e(old('name')); ?>"
                                           class="w-full px-4 py-4 border border-gray-300 rounded-xl focus:outline-none focus:border-green-500 focus:ring-4 focus:ring-green-100 transition-all duration-300 group-hover:border-gray-400"
                                           placeholder="Enter your full name">
                                    <div class="absolute inset-y-0 right-0 flex items-center pr-4">
                                        <i class="fas fa-user text-gray-400 group-focus-within:text-green-500 transition-colors"></i>
                                    </div>
                                </div>
                            </div>

                            <div class="group">
                                <label for="email" class="block text-sm font-semibold text-gray-700 mb-2">Email Address *</label>
                                <div class="relative">
                                    <input type="email" id="email" name="email" required value="<?php echo e(old('email')); ?>"
                                           class="w-full px-4 py-4 border border-gray-300 rounded-xl focus:outline-none focus:border-green-500 focus:ring-4 focus:ring-green-100 transition-all duration-300 group-hover:border-gray-400"
                                           placeholder="<EMAIL>">
                                    <div class="absolute inset-y-0 right-0 flex items-center pr-4">
                                        <i class="fas fa-envelope text-gray-400 group-focus-within:text-green-500 transition-colors"></i>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Phone and Subject Row -->
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div class="group">
                                <label for="phone" class="block text-sm font-semibold text-gray-700 mb-2">Phone Number</label>
                                <div class="relative">
                                    <input type="tel" id="phone" name="phone" value="<?php echo e(old('phone')); ?>"
                                           class="w-full px-4 py-4 border border-gray-300 rounded-xl focus:outline-none focus:border-green-500 focus:ring-4 focus:ring-green-100 transition-all duration-300 group-hover:border-gray-400"
                                           placeholder="+****************">
                                    <div class="absolute inset-y-0 right-0 flex items-center pr-4">
                                        <i class="fas fa-phone text-gray-400 group-focus-within:text-green-500 transition-colors"></i>
                                    </div>
                                </div>
                            </div>

                            <div class="group">
                                <label for="subject" class="block text-sm font-semibold text-gray-700 mb-2">Subject</label>
                                <div class="relative">
                                    <input type="text" id="subject" name="subject" value="<?php echo e(old('subject')); ?>"
                                           class="w-full px-4 py-4 border border-gray-300 rounded-xl focus:outline-none focus:border-green-500 focus:ring-4 focus:ring-green-100 transition-all duration-300 group-hover:border-gray-400"
                                           placeholder="What can we help you with?">
                                    <div class="absolute inset-y-0 right-0 flex items-center pr-4">
                                        <i class="fas fa-tag text-gray-400 group-focus-within:text-green-500 transition-colors"></i>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Message -->
                        <div class="group">
                            <label for="message" class="block text-sm font-semibold text-gray-700 mb-2">Your Message *</label>
                            <div class="relative">
                                <textarea id="message" name="message" rows="6" required
                                          class="w-full px-4 py-4 border border-gray-300 rounded-xl focus:outline-none focus:border-green-500 focus:ring-4 focus:ring-green-100 transition-all duration-300 group-hover:border-gray-400 resize-vertical"
                                          placeholder="Tell us about your logistics needs, timeline, and any specific requirements..."><?php echo e(old('message')); ?></textarea>
                                <div class="absolute top-4 right-4">
                                    <i class="fas fa-comment text-gray-400 group-focus-within:text-green-500 transition-colors"></i>
                                </div>
                            </div>
                        </div>

                        <!-- Newsletter Subscription -->
                        <div class="flex items-start space-x-3 p-4 bg-gray-50 rounded-xl">
                            <input type="checkbox" id="subscribe_newsletter" name="subscribe_newsletter" value="1"
                                   <?php echo e(old('subscribe_newsletter') ? 'checked' : ''); ?>

                                   class="mt-1 w-5 h-5 text-green-600 border-gray-300 rounded focus:ring-green-500 focus:ring-2">
                            <label for="subscribe_newsletter" class="text-gray-700 leading-relaxed">
                                <span class="font-semibold">Stay updated!</span> Subscribe to our newsletter for logistics insights, industry news, and special offers.
                            </label>
                        </div>

                        <!-- Submit Button -->
                        <div class="pt-4">
                            <button type="submit" id="submit-btn"
                                    class="group relative w-full md:w-auto px-8 py-4 bg-gradient-to-r from-green-600 to-blue-600 hover:from-green-700 hover:to-blue-700 text-white font-bold rounded-xl shadow-lg hover:shadow-xl transform hover:-translate-y-1 transition-all duration-300 focus:outline-none focus:ring-4 focus:ring-green-200">
                                <span class="flex items-center justify-center">
                                    <i class="fas fa-paper-plane mr-3 group-hover:translate-x-1 transition-transform duration-300"></i>
                                    <span id="btn-text">Send Message</span>
                                </span>

                                <!-- Loading Spinner -->
                                <div id="loading-spinner" class="hidden absolute inset-0 flex items-center justify-center">
                                    <div class="w-6 h-6 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                                </div>
                            </button>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Sidebar -->
            <div class="lg:col-span-1">
                <div class="space-y-8">
                    <!-- Business Hours Card -->
                    <div class="bg-gradient-to-br from-gray-900 to-gray-800 rounded-2xl p-8 text-white animate-on-scroll">
                        <div class="flex items-center mb-6">
                            <div class="w-12 h-12 bg-green-500 rounded-xl flex items-center justify-center mr-4">
                                <i class="fas fa-clock text-xl"></i>
                            </div>
                            <h3 class="text-2xl font-bold">Business Hours</h3>
                        </div>

                        <div class="space-y-3">
                            <?php if(!empty($siteSettings['business_hours'])): ?>
                                <?php $__currentLoopData = explode("\n", $siteSettings['business_hours']); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $hours): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <div class="flex justify-between items-center py-2 border-b border-gray-700 last:border-b-0">
                                        <?php
                                            $parts = explode(':', $hours, 2);
                                            $day = trim($parts[0] ?? '');
                                            $time = trim($parts[1] ?? '');
                                        ?>
                                        <span class="text-gray-300"><?php echo e($day); ?></span>
                                        <span class="font-semibold text-green-400"><?php echo e($time); ?></span>
                                    </div>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            <?php else: ?>
                                <div class="flex justify-between items-center py-2 border-b border-gray-700">
                                    <span class="text-gray-300">Monday - Friday</span>
                                    <span class="font-semibold text-green-400">8:00 AM - 6:00 PM</span>
                                </div>
                                <div class="flex justify-between items-center py-2 border-b border-gray-700">
                                    <span class="text-gray-300">Saturday</span>
                                    <span class="font-semibold text-green-400">9:00 AM - 4:00 PM</span>
                                </div>
                                <div class="flex justify-between items-center py-2">
                                    <span class="text-gray-300">Sunday</span>
                                    <span class="font-semibold text-red-400">Closed</span>
                                </div>
                            <?php endif; ?>
                        </div>

                        <!-- Emergency Contact -->
                        <div class="mt-6 p-4 bg-red-500/20 border border-red-500/30 rounded-xl">
                            <div class="flex items-center mb-2">
                                <i class="fas fa-exclamation-triangle text-red-400 mr-2"></i>
                                <span class="font-semibold text-red-400">Emergency Support</span>
                            </div>
                            <p class="text-sm text-gray-300">For urgent shipment issues outside business hours</p>
                            <?php if(!empty($siteSettings['phone'])): ?>
                            <a href="tel:<?php echo e($siteSettings['phone']); ?>" class="text-red-400 font-semibold hover:text-red-300 transition-colors">
                                <?php echo e($siteSettings['phone']); ?>

                            </a>
                            <?php endif; ?>
                        </div>
                    </div>

                    <!-- Quick Services Card -->
                    <div class="bg-white rounded-2xl p-8 shadow-lg border border-gray-100 animate-on-scroll">
                        <div class="flex items-center mb-6">
                            <div class="w-12 h-12 bg-blue-500 rounded-xl flex items-center justify-center mr-4">
                                <i class="fas fa-bolt text-xl text-white"></i>
                            </div>
                            <h3 class="text-2xl font-bold text-gray-900">Quick Services</h3>
                        </div>

                        <div class="space-y-4">
                            <a href="<?php echo e(route('tracking.index')); ?>" class="group flex items-center p-4 bg-gray-50 hover:bg-blue-50 rounded-xl transition-all duration-300 hover:shadow-md">
                                <div class="w-10 h-10 bg-blue-100 group-hover:bg-blue-200 rounded-lg flex items-center justify-center mr-4 transition-colors">
                                    <i class="fas fa-search text-blue-600"></i>
                                </div>
                                <div>
                                    <h4 class="font-semibold text-gray-900 group-hover:text-blue-600 transition-colors">Track Package</h4>
                                    <p class="text-sm text-gray-600">Real-time tracking updates</p>
                                </div>
                                <i class="fas fa-arrow-right text-gray-400 group-hover:text-blue-600 group-hover:translate-x-1 transition-all ml-auto"></i>
                            </a>

                            <button onclick="openQuoteModal()" class="group w-full flex items-center p-4 bg-gray-50 hover:bg-green-50 rounded-xl transition-all duration-300 hover:shadow-md">
                                <div class="w-10 h-10 bg-green-100 group-hover:bg-green-200 rounded-lg flex items-center justify-center mr-4 transition-colors">
                                    <i class="fas fa-calculator text-green-600"></i>
                                </div>
                                <div class="text-left">
                                    <h4 class="font-semibold text-gray-900 group-hover:text-green-600 transition-colors">Get Quote</h4>
                                    <p class="text-sm text-gray-600">Instant pricing estimates</p>
                                </div>
                                <i class="fas fa-arrow-right text-gray-400 group-hover:text-green-600 group-hover:translate-x-1 transition-all ml-auto"></i>
                            </button>

                            <a href="<?php echo e(route('services')); ?>" class="group flex items-center p-4 bg-gray-50 hover:bg-purple-50 rounded-xl transition-all duration-300 hover:shadow-md">
                                <div class="w-10 h-10 bg-purple-100 group-hover:bg-purple-200 rounded-lg flex items-center justify-center mr-4 transition-colors">
                                    <i class="fas fa-truck text-purple-600"></i>
                                </div>
                                <div>
                                    <h4 class="font-semibold text-gray-900 group-hover:text-purple-600 transition-colors">Our Services</h4>
                                    <p class="text-sm text-gray-600">Explore all solutions</p>
                                </div>
                                <i class="fas fa-arrow-right text-gray-400 group-hover:text-purple-600 group-hover:translate-x-1 transition-all ml-auto"></i>
                            </a>

                            <?php if(auth()->guard()->check()): ?>
                            <a href="<?php echo e(route('customer.dashboard')); ?>" class="group flex items-center p-4 bg-gray-50 hover:bg-orange-50 rounded-xl transition-all duration-300 hover:shadow-md">
                                <div class="w-10 h-10 bg-orange-100 group-hover:bg-orange-200 rounded-lg flex items-center justify-center mr-4 transition-colors">
                                    <i class="fas fa-user-circle text-orange-600"></i>
                                </div>
                                <div>
                                    <h4 class="font-semibold text-gray-900 group-hover:text-orange-600 transition-colors">Customer Portal</h4>
                                    <p class="text-sm text-gray-600">Manage your account</p>
                                </div>
                                <i class="fas fa-arrow-right text-gray-400 group-hover:text-orange-600 group-hover:translate-x-1 transition-all ml-auto"></i>
                            </a>
                            <?php else: ?>
                            <a href="<?php echo e(route('customer.login')); ?>" class="group flex items-center p-4 bg-gray-50 hover:bg-orange-50 rounded-xl transition-all duration-300 hover:shadow-md">
                                <div class="w-10 h-10 bg-orange-100 group-hover:bg-orange-200 rounded-lg flex items-center justify-center mr-4 transition-colors">
                                    <i class="fas fa-sign-in-alt text-orange-600"></i>
                                </div>
                                <div>
                                    <h4 class="font-semibold text-gray-900 group-hover:text-orange-600 transition-colors">Customer Login</h4>
                                    <p class="text-sm text-gray-600">Access your account</p>
                                </div>
                                <i class="fas fa-arrow-right text-gray-400 group-hover:text-orange-600 group-hover:translate-x-1 transition-all ml-auto"></i>
                            </a>
                            <?php endif; ?>
                        </div>
                    </div>

                    <!-- Social Media Card -->
                    <div class="bg-gradient-to-br from-blue-600 to-purple-600 rounded-2xl p-8 text-white animate-on-scroll">
                        <div class="flex items-center mb-6">
                            <div class="w-12 h-12 bg-white/20 rounded-xl flex items-center justify-center mr-4">
                                <i class="fas fa-share-alt text-xl"></i>
                            </div>
                            <h3 class="text-2xl font-bold">Follow Us</h3>
                        </div>

                        <p class="text-blue-100 mb-6">Stay connected for the latest updates, industry insights, and logistics tips.</p>

                        <div class="grid grid-cols-2 gap-3">
                            <?php if(!empty($siteSettings['facebook_url'])): ?>
                            <a href="<?php echo e($siteSettings['facebook_url']); ?>" target="_blank" class="flex items-center justify-center p-3 bg-white/10 hover:bg-white/20 rounded-lg transition-all duration-300 hover:scale-105">
                                <i class="fab fa-facebook-f text-xl mr-2"></i>
                                <span class="font-semibold">Facebook</span>
                            </a>
                            <?php endif; ?>

                            <?php if(!empty($siteSettings['twitter_url'])): ?>
                            <a href="<?php echo e($siteSettings['twitter_url']); ?>" target="_blank" class="flex items-center justify-center p-3 bg-white/10 hover:bg-white/20 rounded-lg transition-all duration-300 hover:scale-105">
                                <i class="fab fa-twitter text-xl mr-2"></i>
                                <span class="font-semibold">Twitter</span>
                            </a>
                            <?php endif; ?>

                            <?php if(!empty($siteSettings['linkedin_url'])): ?>
                            <a href="<?php echo e($siteSettings['linkedin_url']); ?>" target="_blank" class="flex items-center justify-center p-3 bg-white/10 hover:bg-white/20 rounded-lg transition-all duration-300 hover:scale-105">
                                <i class="fab fa-linkedin-in text-xl mr-2"></i>
                                <span class="font-semibold">LinkedIn</span>
                            </a>
                            <?php endif; ?>

                            <?php if(!empty($siteSettings['instagram_url'])): ?>
                            <a href="<?php echo e($siteSettings['instagram_url']); ?>" target="_blank" class="flex items-center justify-center p-3 bg-white/10 hover:bg-white/20 rounded-lg transition-all duration-300 hover:scale-105">
                                <i class="fab fa-instagram text-xl mr-2"></i>
                                <span class="font-semibold">Instagram</span>
                            </a>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>
<!-- Interactive Map Section -->
<section id="map" class="py-20 bg-gray-900 relative overflow-hidden">
    <!-- Background Effects -->
    <div class="absolute inset-0">
        <div class="absolute top-1/4 left-1/4 w-96 h-96 bg-gradient-to-r from-green-500/10 to-blue-500/10 rounded-full blur-3xl animate-pulse"></div>
        <div class="absolute bottom-1/4 right-1/4 w-80 h-80 bg-gradient-to-r from-blue-500/10 to-green-500/10 rounded-full blur-3xl animate-pulse" style="animation-delay: 1s;"></div>
    </div>

    <div class="container mx-auto px-4 relative z-10">
        <!-- Section Header -->
        <div class="text-center mb-16 animate-on-scroll">
            <h2 class="text-4xl lg:text-5xl font-bold font-heading text-white mb-6">
                Find Our <span class="text-transparent bg-clip-text bg-gradient-to-r from-green-400 to-blue-400">Location</span>
            </h2>
            <p class="text-xl text-gray-300 max-w-3xl mx-auto">
                Visit our headquarters or use our interactive map to plan your route to our facilities.
            </p>
        </div>

        <!-- Map Container -->
        <div class="max-w-6xl mx-auto animate-on-scroll">
            <div class="bg-white rounded-2xl p-2 shadow-2xl">
                <div class="relative h-96 lg:h-[500px] rounded-xl overflow-hidden">
                    <!-- Map Placeholder with Interactive Elements -->
                    <div class="w-full h-full bg-gradient-to-br from-gray-100 to-gray-200 flex items-center justify-center relative">
                        <!-- Map Background Pattern -->
                        <div class="absolute inset-0 opacity-10" style="background-image: url('data:image/svg+xml,<svg width="40" height="40" viewBox="0 0 40 40" xmlns="http://www.w3.org/2000/svg"><g fill="%23000000" fill-opacity="0.1"><path d="M20 20c0-5.5-4.5-10-10-10s-10 4.5-10 10 4.5 10 10 10 10-4.5 10-10zm10 0c0-5.5-4.5-10-10-10s-10 4.5-10 10 4.5 10 10 10 10-4.5 10-10z"/></g></svg>'); background-size: 40px 40px;"></div>

                        <!-- Location Marker -->
                        <div class="relative z-10 text-center">
                            <div class="w-16 h-16 bg-red-500 rounded-full flex items-center justify-center mx-auto mb-4 shadow-lg animate-bounce">
                                <i class="fas fa-map-marker-alt text-white text-2xl"></i>
                            </div>
                            <div class="bg-white rounded-lg p-4 shadow-lg max-w-sm">
                                <h3 class="font-bold text-gray-900 mb-2"><?php echo e($siteSettings['site_name'] ?? 'Atrix Logistics'); ?></h3>
                                <p class="text-gray-600 text-sm mb-3"><?php echo e($siteSettings['address'] ?? 'Flat 20, Reynolds Neck, North Helenaville, FV77 8WS'); ?></p>
                                <div class="flex space-x-2">
                                    <button onclick="getDirections()" class="flex-1 bg-blue-600 hover:bg-blue-700 text-white px-3 py-2 rounded-lg text-sm font-semibold transition-colors">
                                        <i class="fas fa-directions mr-1"></i>
                                        Directions
                                    </button>
                                    <button onclick="shareLocation()" class="bg-gray-600 hover:bg-gray-700 text-white px-3 py-2 rounded-lg text-sm transition-colors">
                                        <i class="fas fa-share-alt"></i>
                                    </button>
                                </div>
                            </div>
                        </div>

                        <!-- Interactive Map Notice -->
                        <div class="absolute bottom-4 left-4 bg-black/70 text-white px-4 py-2 rounded-lg text-sm">
                            <i class="fas fa-info-circle mr-2"></i>
                            Interactive map integration available
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Location Details -->
        <div class="grid grid-cols-1 md:grid-cols-3 gap-8 mt-16 max-w-6xl mx-auto">
            <div class="text-center animate-on-scroll">
                <div class="w-16 h-16 bg-green-500 rounded-2xl flex items-center justify-center mx-auto mb-4">
                    <i class="fas fa-car text-white text-2xl"></i>
                </div>
                <h3 class="text-xl font-bold text-white mb-2">Parking Available</h3>
                <p class="text-gray-300">Free parking spaces available for visitors and clients</p>
            </div>

            <div class="text-center animate-on-scroll">
                <div class="w-16 h-16 bg-blue-500 rounded-2xl flex items-center justify-center mx-auto mb-4">
                    <i class="fas fa-subway text-white text-2xl"></i>
                </div>
                <h3 class="text-xl font-bold text-white mb-2">Public Transport</h3>
                <p class="text-gray-300">Easily accessible by bus and metro lines</p>
            </div>

            <div class="text-center animate-on-scroll">
                <div class="w-16 h-16 bg-purple-500 rounded-2xl flex items-center justify-center mx-auto mb-4">
                    <i class="fas fa-wheelchair text-white text-2xl"></i>
                </div>
                <h3 class="text-xl font-bold text-white mb-2">Accessible</h3>
                <p class="text-gray-300">Wheelchair accessible entrance and facilities</p>
            </div>
        </div>
    </div>
</section>

<?php $__env->stopSection(); ?>

<?php $__env->startPush('styles'); ?>
<style>
/* Custom animations for magical effects */
@keyframes float {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    33% { transform: translateY(-10px) rotate(1deg); }
    66% { transform: translateY(5px) rotate(-1deg); }
}

.animate-float {
    animation: float 6s ease-in-out infinite;
}

/* Form focus effects */
.group:focus-within .fas {
    transform: scale(1.1);
}

/* Button hover effects */
button:hover .fas {
    transform: translateX(2px);
}

/* Scroll animations */
.animate-on-scroll {
    opacity: 0;
    transform: translateY(30px);
    transition: all 0.8s ease-out;
}

.animate-on-scroll.visible {
    opacity: 1;
    transform: translateY(0);
}

/* Gradient text animation */
@keyframes gradient-shift {
    0% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
    100% { background-position: 0% 50%; }
}

.bg-gradient-to-r {
    background-size: 200% 200%;
    animation: gradient-shift 3s ease infinite;
}
</style>
<?php $__env->stopPush(); ?>

<?php $__env->startPush('scripts'); ?>
<script>
// Enhanced contact form submission with animations
document.getElementById('contact-form').addEventListener('submit', function(e) {
    const submitBtn = document.getElementById('submit-btn');
    const btnText = document.getElementById('btn-text');
    const loadingSpinner = document.getElementById('loading-spinner');

    // Show loading state
    submitBtn.disabled = true;
    btnText.classList.add('hidden');
    loadingSpinner.classList.remove('hidden');

    // Add pulse effect to form
    this.style.transform = 'scale(0.98)';
    this.style.transition = 'transform 0.2s ease';

    setTimeout(() => {
        this.style.transform = 'scale(1)';
    }, 200);
});

// Scroll animations
function initScrollAnimations() {
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };

    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.classList.add('visible');
            }
        });
    }, observerOptions);

    document.querySelectorAll('.animate-on-scroll').forEach(el => {
        observer.observe(el);
    });
}

// Map functions
function getDirections() {
    const address = encodeURIComponent("<?php echo e($siteSettings['address'] ?? 'Flat 20, Reynolds Neck, North Helenaville, FV77 8WS'); ?>");
    window.open(`https://www.google.com/maps/dir/?api=1&destination=${address}`, '_blank');
}

function shareLocation() {
    const address = "<?php echo e($siteSettings['address'] ?? 'Flat 20, Reynolds Neck, North Helenaville, FV77 8WS'); ?>";
    const siteName = "<?php echo e($siteSettings['site_name'] ?? 'Atrix Logistics'); ?>";

    if (navigator.share) {
        navigator.share({
            title: `${siteName} Location`,
            text: `Visit us at: ${address}`,
            url: window.location.href
        });
    } else {
        // Fallback: copy to clipboard
        navigator.clipboard.writeText(`${siteName} - ${address}`).then(() => {
            // Show success message
            const toast = document.createElement('div');
            toast.className = 'fixed bottom-4 right-4 bg-green-500 text-white px-6 py-3 rounded-lg shadow-lg z-50 transform translate-y-full transition-transform duration-300';
            toast.textContent = 'Location copied to clipboard!';
            document.body.appendChild(toast);

            setTimeout(() => {
                toast.style.transform = 'translateY(0)';
            }, 100);

            setTimeout(() => {
                toast.style.transform = 'translateY(100%)';
                setTimeout(() => document.body.removeChild(toast), 300);
            }, 3000);
        });
    }
}

// Form field animations
document.querySelectorAll('input, textarea').forEach(field => {
    field.addEventListener('focus', function() {
        this.parentElement.style.transform = 'scale(1.02)';
        this.parentElement.style.transition = 'transform 0.2s ease';
    });

    field.addEventListener('blur', function() {
        this.parentElement.style.transform = 'scale(1)';
    });
});

// Initialize animations when page loads
document.addEventListener('DOMContentLoaded', function() {
    initScrollAnimations();

    // Add stagger effect to contact cards
    const cards = document.querySelectorAll('.animate-on-scroll');
    cards.forEach((card, index) => {
        card.style.transitionDelay = `${index * 0.1}s`;
    });
});

// Floating particles animation
function createFloatingParticle() {
    const particle = document.createElement('div');
    particle.className = 'absolute w-1 h-1 bg-white rounded-full opacity-30';
    particle.style.left = Math.random() * 100 + '%';
    particle.style.top = '100%';
    particle.style.animation = `float ${3 + Math.random() * 4}s linear infinite`;

    document.querySelector('.hero-section')?.appendChild(particle);

    setTimeout(() => {
        particle.remove();
    }, 7000);
}

// Create particles periodically
setInterval(createFloatingParticle, 2000);
</script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('layouts.frontend', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\Users\<USER>\Desktop\Chisolution Inc\Logistics\Atrix\atrix-logistics\resources\views/frontend/contact.blade.php ENDPATH**/ ?>